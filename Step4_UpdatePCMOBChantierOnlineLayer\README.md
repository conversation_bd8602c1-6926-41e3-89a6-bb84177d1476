# Step4 - Update PCMOB_CHANTIER Online Layer

## Overview
This script uploads PCMOB_CHANTIER data from the Export PCM geodatabase to an ArcGIS Online feature service. It follows the same structure and patterns as the existing Step3-UpdatePCMOnlineLayer.py script but uses a different data source and processing approach.

## Purpose
- Upload data from the Export PCM geodatabase to ArcGIS Online
- Target the Carte_PCM_WFL1 FeatureServer 
- Handle data transformation and upload process
- Maintain data integrity and proper error handling

## Data Sources
- **Source**: `C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Export PCM\202507-04\PCMOB_CHANTIER.gdb\PCMOB_CHANTIER`
- **Target**: `https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/`

## Key Features
1. **Data Validation**: Checks if source data exists before processing
2. **Temporary Processing**: Creates temporary geodatabase for safe data handling
3. **Zip Creation**: Packages data for upload to ArcGIS Online
4. **Layer Management**: Properly manages sync settings and capabilities during upload
5. **Data Upload**: Truncates existing data and appends new data
6. **Cleanup**: Removes temporary files and uploaded items
7. **Error Handling**: Comprehensive error handling with detailed logging
8. **Logging**: Timestamped logging for monitoring and debugging

## Process Flow
1. Connect to ArcGIS Online
2. Create temporary geodatabase
3. Copy source data to temporary location
4. Create zip file from temporary geodatabase
5. Upload zip to ArcGIS Online
6. Configure target layer for data upload
7. Truncate existing data in target layer
8. Append new data from uploaded geodatabase
9. Restore layer sync settings and capabilities
10. Clean up temporary files and uploaded items

## Usage
Run the script using the provided batch file:
```
launchScript.bat
```

Or run directly with Python:
```
"C:\Program Files\ArcGIS\Pro\bin\Python\envs\arcgispro-py3\python.exe" "Step4-UpdatePCMOBChantierOnlineLayer.py"
```

## Requirements
- ArcGIS Pro with Python environment
- ArcGIS API for Python
- Valid ArcGIS Online credentials
- Access to source geodatabase
- Write permissions to target feature service

## Error Handling
The script includes comprehensive error handling for:
- Missing source data
- Connection issues to ArcGIS Online
- File system operations
- Data upload failures
- Cleanup operations

## Logging
All operations are logged with timestamps for monitoring and debugging purposes.

## Notes
- The script creates temporary files that are automatically cleaned up
- Existing data in the target layer is truncated before new data is added
- Layer sync capabilities are temporarily disabled during upload for better performance
- The script follows the same patterns as Step3 for consistency with the existing workflow

## Version History
- v1.0 (2025-01-24): Initial version based on Step3-UpdatePCMOnlineLayer auto.py
