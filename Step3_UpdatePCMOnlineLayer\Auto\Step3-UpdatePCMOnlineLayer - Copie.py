import arcpy
import arcgis
from arcgis.gis import GIS
from arcgis.features import FeatureLayerCollection
import sys
import os
import zipfile

# Parameters
#AGOL account settings
agolAccount = r"https://citecing.maps.arcgis.com/"
userId = "citecing"
userPwd = "5I6e7!1801"

#PCMOB CHANTIER FEATURE LAYER: TEST OR PRODUCTION
#publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/CouchesPCM_Test_Update/FeatureServer/2" #TEST
publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/0" #PRODUCTION

#PCMOB CHANTIER FEATURE CLASS & GDB: TEST
#updateFeatures = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM_dev.gdb\PCMOB_CHANTIER" #TEST
updateFeatures = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.gdb\PCMOB_CHANTIER"  #PRODUCTION

zip_path = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.zip"
directory = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.gdb"


def TruncateWebLayer(gis=None, target=None):
    try:
        lyr = arcgis.features.FeatureLayer(target, gis)
        lyr.manager.truncate()
        print ("Successfully truncated layer : " + str(target))
    except:
        print("Failed truncating : " + str(target))
        sys.exit()

# main function
if __name__ == '__main__':
    try:

        print("\nStarting the program")

        #create connection to AGOL portal GIS
        print("Connecting to ArcGIS Online")
        mygis = arcgis.gis.GIS(agolAccount, userId, userPwd)
        url = 'https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/'
        pcm_flc = arcgis.features.FeatureLayerCollection(url, mygis)

        arcpy.env.overwriteOutput = True

        #reference the empty layer as FeatureLayer object from the ArcGIS Python API
        fl = arcgis.features.FeatureLayer(publishedWebLayer, mygis)

        #Truncate the layer
        TruncateWebLayer(mygis, publishedWebLayer)

        print("\nEnd of program successfully")

    except:
        print("Unable to run the script: error ",str(sys.exc_info()))
