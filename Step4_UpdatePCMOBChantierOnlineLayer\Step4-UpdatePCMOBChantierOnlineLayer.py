# -*- coding: utf-8 -*-
"""
Company: Citec
Author: Generated based on Step3-UpdatePCMOnlineLayer.py
Creation date: 2025-01-24
Version: 2.0 - Simplified
Comments:
- Step4 script to upload PCMOB_CHANTIER data from Export PCM geodatabase to ArcGIS Online
- Simplified version using edit_features() method directly
- Eliminates temporary files, zipping, and upload workflow
"""

import arcpy
import arcgis
from arcgis.gis import GIS
from arcgis.features import FeatureLayer
import sys
import os
from datetime import datetime

# Parameters
# AGOL account settings
agolAccount = r"https://citecing.maps.arcgis.com/"
userId = "citecing"
userPwd = "17_c2T4c_23"

# Target Feature Service URL as specified in requirements
publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/0"

# Source data from Export PCM geodatabase as specified in requirements
sourceGdbPath = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Export PCM\202507-04\PCMOB_CHANTIER.gdb"
sourceFeatureClass = "PCMOB_CHANTIER"
sourceFeatures = os.path.join(sourceGdbPath, sourceFeatureClass)


def log_message(message):
    """Log messages with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")


def truncate_web_layer(feature_layer):
    """Truncate the target web layer"""
    try:
        log_message("Truncating web layer")
        feature_layer.manager.truncate()
        log_message("Successfully truncated layer")

    except Exception as e:
        log_message(f"Failed truncating layer: {str(e)}")
        raise


def read_source_features():
    """Read features from source geodatabase and convert to feature set using SearchCursor"""
    try:
        log_message(f"Reading source data from: {sourceFeatures}")

        # Check if source data exists
        if not arcpy.Exists(sourceFeatures):
            raise Exception(f"Source feature class does not exist: {sourceFeatures}")

        # Get feature count for logging
        count = arcpy.management.GetCount(sourceFeatures)
        log_message(f"Found {count} features in source data")

        # Get field information
        log_message("Getting field information from source data")
        field_objects = arcpy.ListFields(sourceFeatures)

        # Create field list for cursor (exclude OID and geometry initially)
        attribute_fields = []
        for field in field_objects:
            if field.type not in [
                "OID",
                "Geometry",
            ] and not field.name.upper().startswith("SHAPE"):
                attribute_fields.append(field.name)

        # Add geometry field (SHAPE@) to get geometry
        cursor_fields = attribute_fields + ["SHAPE@"]
        log_message(f"Reading {len(attribute_fields)} attribute fields plus geometry")

        # Read features using SearchCursor and convert to feature list
        log_message("Converting features using SearchCursor approach")
        features = []

        with arcpy.da.SearchCursor(sourceFeatures, cursor_fields) as cursor:
            for row in cursor:
                # Create attributes dictionary (exclude geometry)
                attributes = {}
                for i, field_name in enumerate(attribute_fields):
                    value = row[i]
                    # Handle None values and convert to appropriate types
                    if value is None:
                        attributes[field_name] = None
                    else:
                        attributes[field_name] = value

                # Get geometry (last item in row)
                geometry = row[-1]

                # Convert ArcPy geometry to dictionary format for ArcGIS API
                if geometry:
                    # Convert to JSON then parse back to dict for API compatibility
                    geom_json = geometry.JSON
                    import json

                    geom_dict = json.loads(geom_json)
                else:
                    geom_dict = None

                # Create feature dictionary
                feature_dict = {"attributes": attributes, "geometry": geom_dict}

                features.append(feature_dict)

        feature_count = len(features)
        log_message(f"Converted {feature_count} features using SearchCursor")

        # Validate feature count
        if feature_count == 0:
            log_message("Warning: No features found in source data")
        elif feature_count != int(str(count)):
            log_message(
                f"Warning: Feature count mismatch - Source: {count}, Converted: {feature_count}"
            )

        # Create FeatureSet from the features list
        log_message("Creating FeatureSet from converted features")
        feature_set = arcgis.features.FeatureSet(features)

        # Validate the feature set
        if not feature_set or not hasattr(feature_set, "features"):
            raise Exception("Failed to create valid feature set from source data")

        log_message(
            f"Successfully created FeatureSet with {len(feature_set.features)} features"
        )
        return feature_set

    except Exception as e:
        log_message(f"Error reading source features: {str(e)}")
        raise


def upload_features_direct(feature_layer, feature_set):
    """Upload features directly using edit_features method"""
    try:
        log_message("Uploading features using edit_features method")

        # Prepare features for upload
        features_to_add = feature_set.features
        log_message(f"Preparing to add {len(features_to_add)} features")

        # Use edit_features to add new features
        result = feature_layer.edit_features(adds=features_to_add)

        # Check results
        if result["addResults"]:
            success_count = sum(1 for r in result["addResults"] if r["success"])
            error_count = len(result["addResults"]) - success_count

            log_message(f"Successfully added {success_count} features")
            if error_count > 0:
                log_message(f"Failed to add {error_count} features")
                # Log first few errors for debugging
                for i, r in enumerate(result["addResults"][:5]):
                    if not r["success"]:
                        log_message(f"Error {i+1}: {r.get('error', 'Unknown error')}")

        return result

    except Exception as e:
        log_message(f"Error uploading features: {str(e)}")
        raise


# Main function
if __name__ == "__main__":
    try:
        log_message("Starting Step4 - Update PCMOB_CHANTIER Online Layer (Simplified)")
        log_message(f"Source data: {sourceFeatures}")
        log_message(f"Target service: {publishedWebLayer}")

        # Set ArcPy environment
        arcpy.env.overwriteOutput = True

        # Connect to ArcGIS Online
        log_message("Connecting to ArcGIS Online")
        mygis = arcgis.gis.GIS(agolAccount, userId, userPwd)
        log_message(f"Successfully connected as: {mygis.properties.user.username}")

        # Get target feature layer
        log_message("Getting target feature layer")
        target_layer = FeatureLayer(publishedWebLayer, mygis)

        # Read source features
        feature_set = read_source_features()

        # Truncate existing data
        truncate_web_layer(target_layer)

        # Upload new features directly
        result = upload_features_direct(target_layer, feature_set)

        log_message("Step4 program completed successfully")

    except Exception as e:
        log_message(f"Unable to run the script: {str(e)}")
        log_message(f"Error details: {str(sys.exc_info())}")
        sys.exit(1)
