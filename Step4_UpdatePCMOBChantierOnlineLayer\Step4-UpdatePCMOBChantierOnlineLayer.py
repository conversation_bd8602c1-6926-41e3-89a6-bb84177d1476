# -*- coding: utf-8 -*-
"""
Company: Citec
Author: Generated based on Step3-UpdatePCMOnlineLayer.py
Creation date: 2025-01-24
Version: 1.0
Comments:
- Step4 script to upload PCMOB_CHANTIER data from Export PCM geodatabase to ArcGIS Online
- Based on Step3-UpdatePCMOnlineLayer auto.py structure and patterns
- Targets the Carte_PCM_WFL1 FeatureServer with new source data
"""

import arcpy
import arcgis
from arcgis.gis import GIS
from arcgis.features import FeatureLayerCollection
import sys
import os
import zipfile
import tempfile
import shutil
from datetime import datetime

# Parameters
# AGOL account settings
agolAccount = r"https://citecing.maps.arcgis.com/"
userId = "citecing"
userPwd = "17_c2T4c_23"

# Target Feature Service URL as specified in requirements
publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/0"
featureServiceUrl = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/"

# Source data from Export PCM geodatabase as specified in requirements
sourceGdbPath = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Export PCM\202507-04\PCMOB_CHANTIER.gdb"
sourceFeatureClass = "PCMOB_CHANTIER"
sourceFeatures = os.path.join(sourceGdbPath, sourceFeatureClass)

# Temporary working paths
temp_dir = tempfile.gettempdir()
temp_gdb_name = f"PCM_Export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.gdb"
temp_gdb_path = os.path.join(temp_dir, temp_gdb_name)
temp_zip_path = os.path.join(temp_dir, f"{temp_gdb_name}.zip")


def log_message(message):
    """Log messages with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")


def create_temp_geodatabase():
    """Create a temporary geodatabase and copy source data"""
    try:
        log_message("Creating temporary geodatabase for processing")
        
        # Create temporary geodatabase
        arcpy.management.CreateFileGDB(temp_dir, temp_gdb_name)
        log_message(f"Created temporary geodatabase: {temp_gdb_path}")
        
        # Check if source data exists
        if not arcpy.Exists(sourceFeatures):
            raise Exception(f"Source feature class does not exist: {sourceFeatures}")
        
        # Copy source feature class to temporary geodatabase
        temp_fc_path = os.path.join(temp_gdb_path, sourceFeatureClass)
        arcpy.management.CopyFeatures(sourceFeatures, temp_fc_path)
        
        # Get feature count for logging
        count = arcpy.management.GetCount(temp_fc_path)
        log_message(f"Copied {count} features from source to temporary geodatabase")
        
        return temp_fc_path
        
    except Exception as e:
        log_message(f"Error creating temporary geodatabase: {str(e)}")
        raise


def create_zip_from_geodatabase():
    """Create a zip file from the temporary geodatabase"""
    try:
        log_message("Creating zip file from temporary geodatabase")
        
        zip_file = zipfile.ZipFile(temp_zip_path, "w", zipfile.ZIP_DEFLATED)
        rootdir = os.path.basename(temp_gdb_path)
        
        for dirpath, dirnames, filenames in os.walk(temp_gdb_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                parentpath = os.path.relpath(filepath, temp_gdb_path)
                arcname = os.path.join(rootdir, parentpath)
                zip_file.write(filepath, arcname)
        
        zip_file.close()
        log_message(f"Zip file created: {temp_zip_path}")
        
        return temp_zip_path
        
    except Exception as e:
        log_message(f"Error creating zip file: {str(e)}")
        raise


def truncate_web_layer(gis, target_url):
    """Truncate the target web layer"""
    try:
        log_message(f"Truncating web layer: {target_url}")
        lyr = arcgis.features.FeatureLayer(target_url, gis)
        lyr.manager.truncate()
        log_message("Successfully truncated layer")
        
    except Exception as e:
        log_message(f"Failed truncating layer: {str(e)}")
        raise


def upload_data_to_agol(gis, zip_path):
    """Upload data to ArcGIS Online and append to feature service"""
    try:
        log_message("Adding zip file to ArcGIS Online content")
        
        # Upload zip file to AGOL
        upload_properties = {
            "title": f"PCM Export Data {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 
            "type": "File Geodatabase", 
            "tags": "PCM,Export,PCMOB_CHANTIER"
        }
        
        item_upload = gis.content.add(data=zip_path, item_properties=upload_properties, folder="PCM")
        log_message("Zip file added to ArcGIS Online content")
        
        # Get feature layer collection
        pcm_flc = arcgis.features.FeatureLayerCollection(featureServiceUrl, gis)
        
        # Disable sync and update capabilities for upload
        log_message("Updating layer capabilities for data upload")
        update_dict = {"syncEnabled": False}
        pcm_flc.manager.update_definition(update_dict)
        
        update_dict2 = {"capabilities": "Create,Delete,Query,Update,Editing,Extract,ChangeTracking"}
        pcm_flc.layers[0].manager.update_definition(update_dict2)
        
        # Truncate existing data
        truncate_web_layer(gis, publishedWebLayer)
        
        # Append new data
        log_message("Appending data to web layer")
        status = pcm_flc.layers[0].append(
            item_id=item_upload.id, 
            upload_format="filegdb", 
            upsert=True, 
            source_table_name=sourceFeatureClass
        )
        log_message(f"Append data status: {str(status)}")
        
        # Re-enable sync and restore full capabilities
        log_message("Restoring layer capabilities")
        update_dict3 = {"syncEnabled": True}
        pcm_flc.manager.update_definition(update_dict3)
        
        update_dict4 = {"capabilities": "Create,Delete,Query,Update,Editing,Extract,Sync,ChangeTracking"}
        pcm_flc.layers[0].manager.update_definition(update_dict4)
        
        # Clean up uploaded item
        item_upload.delete()
        log_message("Temporary upload item deleted from ArcGIS Online")
        
    except Exception as e:
        log_message(f"Error uploading data to ArcGIS Online: {str(e)}")
        raise


def cleanup_temp_files():
    """Clean up temporary files and directories"""
    try:
        log_message("Cleaning up temporary files")
        
        # Remove zip file
        if os.path.exists(temp_zip_path):
            os.remove(temp_zip_path)
            log_message("Temporary zip file deleted")
        
        # Remove temporary geodatabase
        if os.path.exists(temp_gdb_path):
            shutil.rmtree(temp_gdb_path)
            log_message("Temporary geodatabase deleted")
            
    except Exception as e:
        log_message(f"Warning: Error cleaning up temporary files: {str(e)}")


# Main function
if __name__ == '__main__':
    try:
        log_message("Starting Step4 - Update PCMOB_CHANTIER Online Layer program")
        log_message(f"Source data: {sourceFeatures}")
        log_message(f"Target service: {featureServiceUrl}")
        
        # Set ArcPy environment
        arcpy.env.overwriteOutput = True
        
        # Connect to ArcGIS Online
        log_message("Connecting to ArcGIS Online")
        mygis = arcgis.gis.GIS(agolAccount, userId, userPwd)
        log_message(f"Successfully connected as: {mygis.properties.user.username}")
        
        # Create temporary geodatabase and copy source data
        temp_fc_path = create_temp_geodatabase()
        
        # Create zip file from temporary geodatabase
        zip_path = create_zip_from_geodatabase()
        
        # Upload data to ArcGIS Online
        upload_data_to_agol(mygis, zip_path)
        
        # Clean up temporary files
        cleanup_temp_files()
        
        log_message("Step4 program completed successfully")
        
    except Exception as e:
        log_message(f"Unable to run the script: {str(e)}")
        log_message(f"Error details: {str(sys.exc_info())}")
        
        # Attempt cleanup even if there was an error
        try:
            cleanup_temp_files()
        except:
            pass
        
        sys.exit(1)
