# -*- coding: utf-8 -*-
"""
Company: Citec
Author: Generated based on Step3-UpdatePCMOnlineLayer.py
Creation date: 2025-01-24
Version: 2.0 - Simplified
Comments:
- Step4 script to upload PCMOB_CHANTIER data from Export PCM geodatabase to ArcGIS Online
- Simplified version using edit_features() method directly
- Eliminates temporary files, zipping, and upload workflow
"""

import arcpy
import arcgis
from arcgis.gis import GIS
from arcgis.features import FeatureLayer
import sys
import os
from datetime import datetime

# Parameters
# AGOL account settings
agolAccount = r"https://citecing.maps.arcgis.com/"
userId = "citecing"
userPwd = "17_c2T4c_23"

# Target Feature Service URL as specified in requirements
publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/0"

# Source data from Export PCM geodatabase as specified in requirements
sourceGdbPath = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Export PCM\202507-04\PCMOB_CHANTIER.gdb"
sourceFeatureClass = "PCMOB_CHANTIER"
sourceFeatures = os.path.join(sourceGdbPath, sourceFeatureClass)


def log_message(message):
    """Log messages with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")


def truncate_web_layer(feature_layer):
    """Truncate the target web layer"""
    try:
        log_message("Truncating web layer")
        feature_layer.manager.truncate()
        log_message("Successfully truncated layer")

    except Exception as e:
        log_message(f"Failed truncating layer: {str(e)}")
        raise


def read_source_features():
    """Read features from source geodatabase and convert to feature set"""
    feature_layer = None
    try:
        log_message(f"Reading source data from: {sourceFeatures}")

        # Check if source data exists
        if not arcpy.Exists(sourceFeatures):
            raise Exception(f"Source feature class does not exist: {sourceFeatures}")

        # Get feature count for logging
        count = arcpy.management.GetCount(sourceFeatures)
        log_message(f"Found {count} features in source data")

        # Create a feature layer from the feature class path
        layer_name = "temp_source_layer"
        log_message("Creating temporary feature layer for conversion")
        feature_layer = arcpy.management.MakeFeatureLayer(sourceFeatures, layer_name)[0]

        # Convert feature layer to feature set for API compatibility
        log_message("Converting feature layer to feature set")
        feature_set = arcgis.features.FeatureSet.from_arcpy(feature_layer)

        # Validate the conversion
        if not feature_set or not hasattr(feature_set, "features"):
            raise Exception("Failed to create valid feature set from source data")

        feature_count = len(feature_set.features) if feature_set.features else 0
        log_message(f"Converted {feature_count} features to feature set")

        if feature_count == 0:
            log_message("Warning: No features found in source data")
        elif feature_count != int(str(count)):
            log_message(
                f"Warning: Feature count mismatch - Source: {count}, Converted: {feature_count}"
            )

        return feature_set

    except Exception as e:
        log_message(f"Error reading source features: {str(e)}")
        raise
    finally:
        # Clean up the temporary feature layer if it was created
        if feature_layer and arcpy.Exists(feature_layer):
            try:
                arcpy.management.Delete(feature_layer)
                log_message("Cleaned up temporary feature layer")
            except Exception as cleanup_error:
                # Don't fail the whole process if cleanup fails
                log_message(
                    f"Warning: Could not clean up temporary feature layer: {str(cleanup_error)}"
                )


def upload_features_direct(feature_layer, feature_set):
    """Upload features directly using edit_features method"""
    try:
        log_message("Uploading features using edit_features method")

        # Prepare features for upload
        features_to_add = feature_set.features
        log_message(f"Preparing to add {len(features_to_add)} features")

        # Use edit_features to add new features
        result = feature_layer.edit_features(adds=features_to_add)

        # Check results
        if result["addResults"]:
            success_count = sum(1 for r in result["addResults"] if r["success"])
            error_count = len(result["addResults"]) - success_count

            log_message(f"Successfully added {success_count} features")
            if error_count > 0:
                log_message(f"Failed to add {error_count} features")
                # Log first few errors for debugging
                for i, r in enumerate(result["addResults"][:5]):
                    if not r["success"]:
                        log_message(f"Error {i+1}: {r.get('error', 'Unknown error')}")

        return result

    except Exception as e:
        log_message(f"Error uploading features: {str(e)}")
        raise


# Main function
if __name__ == "__main__":
    try:
        log_message("Starting Step4 - Update PCMOB_CHANTIER Online Layer (Simplified)")
        log_message(f"Source data: {sourceFeatures}")
        log_message(f"Target service: {publishedWebLayer}")

        # Set ArcPy environment
        arcpy.env.overwriteOutput = True

        # Connect to ArcGIS Online
        log_message("Connecting to ArcGIS Online")
        mygis = arcgis.gis.GIS(agolAccount, userId, userPwd)
        log_message(f"Successfully connected as: {mygis.properties.user.username}")

        # Get target feature layer
        log_message("Getting target feature layer")
        target_layer = FeatureLayer(publishedWebLayer, mygis)

        # Read source features
        feature_set = read_source_features()

        # Truncate existing data
        truncate_web_layer(target_layer)

        # Upload new features directly
        result = upload_features_direct(target_layer, feature_set)

        log_message("Step4 program completed successfully")

    except Exception as e:
        log_message(f"Unable to run the script: {str(e)}")
        log_message(f"Error details: {str(sys.exc_info())}")
        sys.exit(1)
