# -*- coding: utf-8 -*-
"""
Generated by ArcGIS ModelBuilder on : 2020-09-15 21:33:09
"""
import arcpy
from sys import argv

def AlarmesChantiersProximite(Expression="DATE_FIN > timestamp '2020-08-27 09:00:00' And (CHANTIER_PCM = 'Oui' Or CHANTIER_PCM = 'Oui_infomobilite')", Search_Radius="100 Meters", PCMOB_CHANTIER="PCMOB_CHANTIER"):  # Alarmes Chantiers Proximité

    # To allow overwriting outputs change overwriteOutput option to True.
    arcpy.env.overwriteOutput = False

    RESEAU_ROUTIER = "RESEAU ROUTIER"

    # Process: Select Layer By Attribute (Select Layer By Attribute) (management)
    PCMOB_CHANTIER_2_, Count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=PCMOB_CHANTIER, selection_type="NEW_SELECTION", where_clause=Expression, invert_where_clause="")

    # Process: Select Layer By Location (Select Layer By Location) (management)
    PCMOB_CHANTIER_3_, Output_Layer_Names, Count_2_ = arcpy.management.SelectLayerByLocation(in_layer=[PCMOB_CHANTIER_2_], overlap_type="INTERSECT", select_features=RESEAU_ROUTIER, search_distance="5 Meters", selection_type="SUBSET_SELECTION", invert_spatial_relationship="NOT_INVERT")

    # Process: Generate Near Table (Generate Near Table) (analysis)
    PCMOBCHANTIER4_GenerateNearT = "C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\PCM Chantier.gdb\\PCMOBCHANTIER4_GenerateNearT"
    arcpy.analysis.GenerateNearTable(in_features=PCMOB_CHANTIER_3_, near_features=Output_Layer_Names, out_table=PCMOBCHANTIER4_GenerateNearT, search_radius=Search_Radius, location="NO_LOCATION", angle="NO_ANGLE", closest="CLOSEST", closest_count=0, method="PLANAR")

    # Process: Add Join (Add Join) (management)
    PCMOB_CHANTIER_3__19 = arcpy.management.AddJoin(in_layer_or_view=PCMOB_CHANTIER_3_, in_field="OBJECTID", join_table=PCMOBCHANTIER4_GenerateNearT, join_field="IN_FID", join_type="KEEP_ALL")[0]

    # Process: Calculate Field (Calculate Field) (management)
    PCMOB_CHANTIER_2__21 = arcpy.management.CalculateField(in_table=PCMOB_CHANTIER_3__19, field="PCMOB_CHANTIER.AL_CHANTIERSPROXRESEAU", expression="returnAlarmeDistance(!PCMOBCHANTIER4_GenerateNearT.NEAR_DIST!)", expression_type="PYTHON3", code_block="def returnAlarmeDistance(dist)
    if dist >= 0:
        return 1
    
    return 0", field_type="TEXT")[0]

if __name__ == '__main__':
    # Global Environment settings
    with arcpy.EnvManager(scratchWorkspace=r"C:\Users\<USER>\CITEC\CITEC - 11012.1 PCM complément RDV étude\4 Etude\18 ArcGIS\Travail\PCM Chantier.gdb", workspace=r"C:\Users\<USER>\CITEC\CITEC - 11012.1 PCM complément RDV étude\4 Etude\18 ArcGIS\Travail\PCM Chantier.gdb"):
        AlarmesChantiersProximite(*argv[1:])
