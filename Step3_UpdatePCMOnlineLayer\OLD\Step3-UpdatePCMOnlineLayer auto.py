import arcpy
import arcgis
from arcgis.gis import GIS
from arcgis.features import FeatureLayerCollection
import sys
import os
import zipfile

# Parameters
#AGOL account settings
agolAccount = r"https://citecing.maps.arcgis.com/"
userId = "citecing"
userPwd = "5I6e7!1801"

#PCMOB CHANTIER FEATURE LAYER: TEST OR PRODUCTION
#publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/CouchesPCM_Test_Update/FeatureServer/2" #TEST
publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/0" #PRODUCTION

#PCMOB CHANTIER FEATURE CLASS & GDB: TEST
#updateFeatures = r"C:\Users\<USER>\CITEC\CITEC - 11012.1 PCM complément RDV étude\4 Etude\18 ArcGIS\Travail\PCM_dev.gdb\PCMOB_CHANTIER" #TEST
updateFeatures = r"C:\Users\<USER>\CITEC\CITEC - 11012.1 PCM complément RDV étude\4 Etude\18 ArcGIS\Travail\PCM Chantier.gdb\PCMOB_CHANTIER"  #PRODUCTION

zip_path = r"C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\PCM Chantier.zip"
directory = r"C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\PCM Chantier.gdb"


def TruncateWebLayer(gis=None, target=None):
    try:
        lyr = arcgis.features.FeatureLayer(target, gis)
        lyr.manager.truncate()
        print ("Successfully truncated layer : " + str(target))
    except:
        print("Failed truncating : " + str(target))
        sys.exit()

# main function
if __name__ == '__main__':
    try:
        
        print("\nStarting the program")

        #create connection to AGOL portal GIS
        print("Connecting to ArcGIS Online")
        mygis = arcgis.gis.GIS(agolAccount, userId, userPwd)  
        url = 'https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/'
        pcm_flc = arcgis.features.FeatureLayerCollection(url, mygis) 

        arcpy.env.overwriteOutput = True
            
        #reference the empty layer as FeatureLayer object from the ArcGIS Python API
        fl = arcgis.features.FeatureLayer(publishedWebLayer, mygis)

        #create a zip file from the gdb
            
        print("Create a zip file from the GDB")
        zip_file = zipfile.ZipFile(zip_path, "w")

        rootdir = os.path.basename(directory)

        for dirpath, dirnames, filenames in os.walk(r"C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\PCM Chantier.gdb"):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                parentpath = os.path.relpath(filepath, directory)
                arcname = os.path.join(rootdir, parentpath)
        
                zip_file.write(filepath, arcname)

        #zip_file.write(r"C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\PCM Chantier.gdb", compress_type=zipfile.ZIP_DEFLATED)
        zip_file.close()
        print("Zip file created")
            
        print("Adding the zip file to ArcGIS Online content")
        new_pcm_properties = {"title": "New PCM", "type": "File Geodatabase", "tags": "PCM"}
        item_new_pcm = mygis.content.add(data = zip_path, item_properties = new_pcm_properties, folder = "PCM")
        print("Zip file added to ArcGIS Online content\n")
        
        #remove all features from the already published feature layer
        print("Truncating Web Layer")
        #Disable the sync of the layer
        update_dict = {"syncEnabled": False}
        pcm_flc.manager.update_definition(update_dict)

        update_dict2 = {"capabilities": "Create,Delete,Query,Update,Editing,Extract,ChangeTracking"}
        pcm_flc.layers[0].manager.update_definition(update_dict2)
        #print("Capabilities : " + str(pcm_flc.layers[0].properties.capabilities))

        #Truncate the layer
        TruncateWebLayer(mygis, publishedWebLayer)

        print("\nAdding data Web Layer")
        status = pcm_flc.layers[0].append(item_id = item_new_pcm.id, upload_format = "filegdb", upsert = True, source_table_name = "PCMOB_CHANTIER")
        print("Append data = " + str(status))

        #Put back the sync of the layer
        update_dict3 = {"syncEnabled": True}
        pcm_flc.manager.update_definition(update_dict3)

        update_dict4 = {"capabilities": "Create,Delete,Query,Update,Editing,Extract,Sync,ChangeTracking"}
        pcm_flc.layers[0].manager.update_definition(update_dict4)
        #print("Capabilities : " + str(pcm_flc.layers[0].properties.capabilities))
        #Delete temp file
        item_new_pcm.delete()
        os.remove(zip_path)
        print("\nTemp file deleted")

        print("\nEnd of program successfully")

    except:
        print("Unable to run the script: error ",str(sys.exc_info()))