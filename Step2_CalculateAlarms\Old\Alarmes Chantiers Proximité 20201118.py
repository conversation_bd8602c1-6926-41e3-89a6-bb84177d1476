# -*- coding: utf-8 -*-
"""Generated by ArcGIS ModelBuilder on: 2020-11-18 12:00:43
All ModelBuilder functionality may not be exported. Edits may be required for equivalency with the original model.
"""

import arcpy

# To allow overwriting the outputs change the overwrite option to true.
arcpy.env.overwriteOutput = False

# Script parameters
Expression = arcpy.GetParameterAsText(0) or "DATE_FIN > timestamp '2020-08-27 09:00:00' And (CHANTIER_PCM = 'Oui' Or CHANTIER_PCM = 'Oui_infomobilite')"
Search_Radius = arcpy.GetParameterAsText(1) or "100 Meters"
PCMOB_CHANTIER = arcpy.GetParameterAsText(2) or "PCMOB_CHANTIER"
# Local variables:
PCMOB_CHANTIER__2_ = PCMOB_CHANTIER
Count = "101"
PCMOB_CHANTIER__3_ = PCMOB_CHANTIER__2_
Output_Layer_Names = PCMOB_CHANTIER__2_
RESEAU_ROUTIER = "RESEAU_ROUTIER"
RESEAU_ROUTIER__3_ = RESEAU_ROUTIER
Count__2_ = "100"
PCMOBCHANTIER4_GenerateNearT = r"C:\Users\<USER>\CITEC\CITEC - 11012.1 PCM complément RDV étude\4 Etude\18 ArcGIS\Travail\PCM Chantier.gdb\PCMOBCHANTIER4_GenerateNearT"
Count__3_ = "2996"

# Process: Select Layer By Attribute
arcpy.SelectLayerByAttribute_management(in_layer_or_view=PCMOB_CHANTIER, selection_type="NEW_SELECTION", where_clause=Expression, invert_where_clause="")

# Process: Select Layer By Attribute (2)
arcpy.SelectLayerByAttribute_management(in_layer_or_view=RESEAU_ROUTIER, selection_type="NEW_SELECTION", where_clause="HIERARCHIE IN ('Réseau primaire', 'Réseau secondaire')", invert_where_clause="")

# Process: Select Layer By Location
arcpy.SelectLayerByLocation_management(in_layer="PCMOB_CHANTIER", overlap_type="INTERSECT", select_features=RESEAU_ROUTIER__3_, search_distance="5 Meters", selection_type="SUBSET_SELECTION", invert_spatial_relationship="NOT_INVERT")

# Process: Generate Near Table
arcpy.GenerateNearTable_analysis(in_features=PCMOB_CHANTIER__3_, near_features=Output_Layer_Names, out_table=PCMOBCHANTIER4_GenerateNearT, search_radius=Search_Radius, location="NO_LOCATION", angle="NO_ANGLE", closest="CLOSEST", closest_count="0", method="PLANAR")

# Process: Add Join
arcpy.AddJoin_management(in_layer_or_view=PCMOB_CHANTIER__3_, in_field="OBJECTID", join_table=PCMOBCHANTIER4_GenerateNearT, join_field="IN_FID", join_type="KEEP_ALL")

# Process: Calculate Field
arcpy.CalculateField_management(in_table=PCMOB_CHANTIER__3_, field="PCMOB_CHANTIER.AL_CHANTIERSPROXRESEAU", expression="returnAlarmeDistance(!PCMOBCHANTIER4_GenerateNearT.NEAR_DIST!)", expression_type="PYTHON3", code_block="def returnAlarmeDistance(dist):
    if dist >= 0:
        return 1
    
    return 0")

