import arcpy
arcpy.management.Append("'PCMOB_CHANTIER MAI 2020'", r"C:\Users\<USER>\OneDrive - CITEC\PROJETS\11012.1 Plateforme Chantier Mobilité\SIG\PC<PERSON> Chantier\PCM Chantier.gdb\PCMOB_CHANTIER", "NO_TEST", 'ID "ID" true true false 4 Long 0 0,First,#;NOM "NOM" true true false 50 Text 0 0,First,#,PCMOB_CHANTIER MAI 2020,PCMOB_CHANTIER.NOM,0,50;TYPE_CHANTIER "TYPE_CHANTIER" true true false 100 Text 0 0,First,#,PCMOB_CHANTIER MAI 2020,PCMOB_CHANTIER.CHANTIER_PCM,0,20;MOA_PCM "MOA_PCM" true true false 4 Long 0 0,First,#;ID_CHANTIER_MOA "ID_CHANTIER_MOA" true true false 100 Text 0 0,First,#;MOA_CONTACT "MOA_CONTACT" true true false 100 Text 0 0,First,#;MOA_TELEPHONE "MOA_TELEPHONE" true true false 100 Text 0 0,First,#;MOA_EMAIL "MOA_EMAIL" true true false 100 Text 0 0,First,#;COMMUNE "COMMUNE" true true false 50 Text 0 0,First,#;QUARTIER "QUARTIER" true true false 50 Text 0 0,First,#;VOIE_NOM "VOIE_NOM" true true false 100 Text 0 0,First,#;VOIE_HIERARCHIE "VOIE_HIERARCHIE" true true false 50 Text 0 0,First,#;VOIE_REMARQUE "VOIE_REMARQUE" true true false 1000 Text 0 0,First,#;AFFECTE_CYCLE "AFFECTE_CYCLE" true true false 50 Text 0 0,First,#;AFFECTE_TC "AFFECTE_TC" true true false 50 Text 0 0,First,#;AFFECTE_PIETON "AFFECTE_PIETON" true true false 50 Text 0 0,First,#;NATURE_TRAVAUX "NATURE_TRAVAUX" true true false 1000 Text 0 0,First,#;REMARQUE_TRAVAUX "REMARQUE_TRAVAUX" true true false 1000 Text 0 0,First,#;DATE_DEBUT "DATE_DEBUT" true true false 8 Date 0 0,First,#;DATE_FIN "DATE_FIN" true true false 8 Date 0 0,First,#;DATE_DUREE "DATE_DUREE" true true false 2 Short 0 0,First,#;DATE_STATUT "DATE_STATUT" true true false 20 Text 0 0,First,#;DATE_REMARQUE "DATE_REMARQUE" true true false 1000 Text 0 0,First,#;CREATION "CREATION" true true false 8 Date 0 0,First,#;EDITION "EDITION" true true false 8 Date 0 0,First,#;HORIZON_TEMPOREL "HORIZON_TEMPOREL" true true false 50 Text 0 0,First,#;IMPACT_GLOBAL "IMPACT_GLOBAL" true true false 20 Text 0 0,First,#;IMPACT_TM "IMPACT_TM" true true false 20 Text 0 0,First,#;IMPACT_TC "IMPACT_TC" true true false 20 Text 0 0,First,#;IMPACT_REMARQUE "IMPACT_REMARQUE" true true false 1000 Text 0 0,First,#;IMPACT_CYCLE "IMPACT_CYCLE" true true false 20 Text 0 0,First,#;IMPACT_PIETON "IMPACT_PIETON" true true false 20 Text 0 0,First,#;CHANTIER_PCM "CHANTIER_PCM" true true false 20 Text 0 0,First,#;MOA_EXPLICITE "MOA_EXPLICITE" true true false 50 Text 0 0,First,#;REGION_DGM "REGION_DGM" true true false 50 Text 0 0,First,#;COMMUNICATION_PCM "COMMUNICATION_PCM" true true false 10 Text 0 0,First,#;IMPACT_VALIDE "IMPACT_VALIDE" true true false 2 Short 0 0,First,#;INFO_URL "INFO_URL" true true false 200 Text 0 0,First,#;DATE_LABEL "DATE_LABEL" true true false 8 Date 0 0,First,#;RDV_ETUDE "RDV_ETUDE" true true false 50 Text 0 0,First,#;NUMERO_PCM "NUMERO_PCM" true true false 4 Long 0 0,First,#;STATUT_CCTSS "STATUT_CCTSS" true true false 50 Text 0 0,First,#;TACHES "TACHES" true true false 32000 Text 0 0,First,#;NO_SOUMISSION "NO SOUMISSION " true true false 255 Text 0 0,First,#;INSPECTEUR_OCT "INSPECTEUR OCT " true true false 255 Text 0 0,First,#;COVID_19 "COVID-19" true true false 255 Text 0 0,First,#;REPORTING "REPORTING" true true false 255 Text 0 0,First,#', '', '')
