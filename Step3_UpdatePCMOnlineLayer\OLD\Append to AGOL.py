import arcpy
arcpy.management.Append("<PERSON><PERSON><PERSON>_CHANTIER", "PCMOB CHANTIER ONLINE", "NO_TEST", "TYPE_CHANTIER \"TYPE DU CHANTIER\" true true false 100 Text 0 0,First,#,PCMOB_CHANT" +
    "IER,TYPE_CHANTIER,0,100;ID_CHANTIER_MOA \"N° METIER\" true true false 100 Text 0 0" +
    ",First,#,PCMOB_CHANTIER,ID_CHANTIER_MOA,0,100;COMMUNE \"COMMUNE\" true true false " +
    "50 Text 0 0,First,#,PCMOB_CHANTIER,COMMUNE,0,50;VOIE_NOM \"VOIE\" true true false " +
    "100 Text 0 0,First,#,PCMOB_CHANTIER,VOIE_NOM,0,100;DATE_DEBUT \"DATE DE DEBUT\" tr" +
    "ue true false 8 Date 0 0,First,#,PCMOB_CHANTIER,DATE_DEBUT,-1,-1;D<PERSON><PERSON>_<PERSON>IN \"DATE " +
    "<PERSON> FIN\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIER,DATE_FIN,-1,-1;CHAN<PERSON><PERSON>" +
    "R_PCM \"LABEL PCM\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIER,CHANTIER_PC" +
    "M,0,20;MOA_EXPLICITE \"MAITRE D\'OUVRAGE\" true true false 50 Text 0 0,First,#,PCMO" +
    "B_CHANTIER,MOA_EXPLICITE,0,50;REGION_DGM \"REGION\" true true false 50 Text 0 0,Fi" +
    "rst,#,PCMOB_CHANTIER,REGION_DGM,0,50;COMMUNICATION_PCM \"COMMUNICATION PCM\" true " +
    "true false 10 Text 0 0,First,#,PCMOB_CHANTIER,COMMUNICATION_PCM,0,10;RDV_ETUDE \"" +
    "RDV ETUDE\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIER,RDV_ETUDE,0,50;NUM" +
    "ERO_PCM \"N° PCM\" true true false 0 Long 0 0,First,#,PCMOB_CHANTIER,NUMERO_PCM,-1" +
    ",-1;TACHES \"TACHES\" true true false 32000 Text 0 0,First,#,PCMOB_CHANTIER,TACHES" +
    ",0,32000;NO_SOUMISSION \"NO SOUMISSION \" true true false 255 Text 0 0,First,#,PCM" +
    "OB_CHANTIER,NO_SOUMISSION,0,255;INSPECTEUR_OCT \"INSPECTEUR OCT \" true true false" +
    " 255 Text 0 0,First,#,PCMOB_CHANTIER,INSPECTEUR_OCT,0,255;REPORTING \"REPORTING\" " +
    "true true false 255 Text 0 0,First,#,PCMOB_CHANTIER,REPORTING,0,255;GlobalID \"Gl" +
    "obalID\" false false true 38 GlobalID 0 0,First,#;AL_LBLPCM_NonDefini \"Chantiers " +
    "dont le label PCM \" true true false 0 Long 0 0,First,#;ALARMES \"ALARMES\" true tr" +
    "ue false 0 Short 0 0,First,#,PCMOB_CHANTIER,ALARMES,-1,-1;AL_CHANTIERSUPTROISMOI" +
    "S \"ALARME CHANTIER SUP 3 MOIS\" true true false 0 Long 0 0,First,#;AL_CHANTIERSPR" +
    "OXRESEAU \"ALARME CHANTIER PROXIMITE RESEAU\" true true false 0 Double 0 0,First,#" +
    ",PCMOB_CHANTIER,AL_CHANTIERSPROXRESEAU,-1,-1;SUIVI_INTERNE \"SUIVI INTERNE\" true " +
    "true false 255 Text 0 0,First,#;VOIE_HIERARCHIE \"HIERARCHIE\" true true false 50 " +
    "Text 0 0,First,#,PCMOB_CHANTIER,VOIE_HIERARCHIE,0,50;DATE_DUREE \"DUREE\" true tru" +
    "e false 0 Long 0 0,First,#,PCMOB_CHANTIER,DATE_DUREE,-1,-1;SUSPENS_OCT \"SUSPENS_" +
    "OCT\" true true false 255 Text 0 0,First,#,PCMOB_CHANTIER,SUSPENS_OCT,0,255;MOBIL" +
    "ITE \"MOBILITE\" true true false 10000 Text 0 0,First,#,PCMOB_CHANTIER,MOBILITE,0," +
    "10000;SUSPENS_GO \"SUSPENS_GO\" true true false 255 Text 0 0,First,#,PCMOB_CHANTIE" +
    "R,SUSPENS_GO,0,255;COVID_19 \"COVID-19\" true true false 256 Text 0 0,First,#;NOM " +
    "\"NOM DU CHANTIER\" true true false 256 Text 0 0,First,#,PCMOB_CHANTIER,NOM,0,50;C" +
    "OM_VOIE \"COM_VOIE\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIER,COM_VOIE," +
    "0,100;COM_TYPE_CHANTIER \"COM_TYPE_CHANTIER\" true true false 100 Text 0 0,First,#" +
    ",PCMOB_CHANTIER,COM_TYPE_CHANTIER,0,100;COM_DATE_DEBUT \"COM_DATE_DEBUT\" true tru" +
    "e false 0 Date 0 0,First,#,PCMOB_CHANTIER,COM_DATE_DEBUT,-1,-1;COM_DATE_FIN \"COM" +
    "_DATE_FIN\" true true false 0 Date 0 0,First,#,PCMOB_CHANTIER,COM_DATE_FIN,-1,-1;" +
    "COM_MOA \"COM_MOA\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIER,COM_MOA,0,5" +
    "0;IMPACT_REMARQUE \"IMPACT_REMARQUE\" true true false 1000 Text 0 0,First,#,PCMOB_" +
    "CHANTIER,IMPACT_REMARQUE,0,1000", '', '')
