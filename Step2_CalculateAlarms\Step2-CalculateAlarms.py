# -*- coding: utf-8 -*-
"""
Company: Citec
Author: SCH
Creation date: 2020-09-14
Modified by : MAM
Update date: 2020-11-18
Version: 1.1
Comments:
- V 1.1 : Added selected RESEAU_ROUTIER by hierarchy and a second label Filter for alarm 1 2020-11-18
"""
import sys
import arcpy
import datetime
from sys import argv
import os
from datetime import date

# ERROR CODES
CONST_PROCESSING_SUCCEEDED = 0
CONST_PROGRAM_INVALID_PARAMETERS = 1
CONST_PROGRAM_INVALID_GDBPATH = 2
CONST_PROGRAM_INVALID_ALARM_CALCULATION_OPTION = 3
CONST_PROGRAM_INVALID_RESET_ALARM_VALUES = 4
CONST_ALARME3_CALCULATE_PROXIMITE = 5
CONST_ALARME3_UPDATE_FIELD = 6
CONST_ERROR_RESET_ALARM_FIELDVALUES = 7
CONST_ERROR_CALCULATING_ALARM1 = 8
CONST_ERROR_CALCULATING_ALARM2 = 9
CONST_ERROR_CALCULATING_ALARM_STATISTICS = 9

# CONSTANTS

CONST_VERBOSE = False # use to display detailled information messages to console
CONST_TRUE = "TRUE"
CONST_FALSE = "FALSE"

# ALARM CALCULATION OPTION
CONST_ALARM_CALCULATION_ALL = "ALL"
CONST_ALARM_CALCULATION_ALARM1 = "1"
CONST_ALARM_CALCULATION_ALARM2 = "2"
CONST_ALARM_CALCULATION_ALARM3 = "3"

# GDB and FC settings
CONST_WORKING_PCM_CHANTIER_GDB = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.gdb"
#CONST_WORKING_PCM_CHANTIER_GDB = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM_Dev.gdb"
CONST_WORKING_FEATURE_CLASS = "PCMOB_CHANTIER"
CONST_STREET_FEATURE_CLASS = "RESEAU_ROUTIER"
CONST_NEAR_TABLE = "PCM_GenerateNearT"

# Field names
CONST_FIELDNAME_OBJECTID = "OBJECTID"
CONST_FIELDNAME_DATEDEBUT = "DATE_DEBUT"
CONST_FIELDNAME_DATEFIN = "DATE_FIN"
CONST_FIELDNAME_ALARMES = "ALARMES"
CONST_FIELDNAME_ALCHANTIERSPROXRESEAU = "AL_CHANTIERSPROXRESEAU"
CONST_FIELDNAME_LABEL_PCM = "CHANTIER_PCM" # label PCM

# select setting
CONST_FILTER1_LABEL_PCM = "Pas_defini" # used for Alarm 1
CONST_FILTER2_LABEL_PCM = "Oui_a_confirmer" # used for Alarm 1 Added by MAM2020-11-18

# used for Alarm 3
CONST_SELECT_DATE = "TODAY"
CONST_PROXIMITY_SELECT_EXPRESSION_DATE = "DATE_FIN > timestamp "
#CONST_PROXIMITY_SELECT_EXPRESSION_CHANTIER = " And (CHANTIER_PCM = 'Oui' Or CHANTIER_PCM = 'Oui_infomobilite' Or CHANTIER_PCM = 'Oui_a_confirmer Or CHANTIER_PCM = 'Non')"
CONST_RESEAU_ROUTIER_EXPRESSION = "HIERARCHIE IN ('Réseau primaire', 'Réseau secondaire')"
CONST_PROXIMITY_SELECT_EXPRESSION_CHANTIER = " And (CHANTIER_PCM = 'Oui' Or CHANTIER_PCM = 'Oui_infomobilite')"
CONST_MIN_FENETRE_TEMPS = 7 # number of days
CONST_SEARCH_DISTANCE = 5 # unit meters
CONST_SEARCH_RADIUS = 100 # unit meters


# Alarmes field values
CONST_CHANTIERS_NA = 0
CONST_CHANTIERS_LABELPCM_NONDEFINI = 1
CONST_CHANTIERS_FIN_MOINS30JOURS = 2
CONST_CHANTIERS_IMPACTANTSPROCHES = 3
CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES = 4
CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES = 5
CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS = 6
CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES = 7

# Reset Alarm Field Values to 0
def Reset_AlarmFieldValues(pcmFC=CONST_WORKING_FEATURE_CLASS):
    try:
        print("\nResetting all alarm field values to {} to all features in {}".format(CONST_CHANTIERS_NA,pcmFC))
        arcpy.management.CalculateField(pcmFC, CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_NA, "PYTHON3", '')

        return CONST_PROCESSING_SUCCEEDED

    except:
        e = sys.exc_info()[1]
        print("Error in Reset_AlarmFieldValue: "+ e.args[0])
        return CONST_ERROR_RESET_ALARM_FIELDVALUES


# Generate statitics
def Compute_AlarmStatistics(pcmFC=CONST_WORKING_FEATURE_CLASS):
    try:
        print("\nGenerating statistics for  {} found".format(CONST_FIELDNAME_ALARMES))

        print(" Number of alarms:")
        TotalAlarms = 0
        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" - 1 only: "+ str(count))
        TotalAlarms = TotalAlarms + int(count)

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_FIN_MOINS30JOURS)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" - 2 only: "+ str(count))
        TotalAlarms = TotalAlarms + int(count)

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" - 3 only: "+ str(count))
        TotalAlarms = TotalAlarms + int(count)

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" - 1 and 2: "+ str(count))
        TotalAlarms = TotalAlarms + int(count)

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" - 1 and 3: "+ str(count))
        TotalAlarms = TotalAlarms + int(count)

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" - 2 and 3: "+ str(count))
        TotalAlarms = TotalAlarms + int(count)

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" - 1, 2, 3: "+ str(count))
        TotalAlarms = TotalAlarms + int(count)

        print(" Total number of alarms: "+ str(TotalAlarms))

        return CONST_PROCESSING_SUCCEEDED

    except:
        e = sys.exc_info()[1]
        print("Error in Compute_AlarmStatistics: "+ e.args[0])
        return CONST_ERROR_CALCULATING_ALARM_STATISTICS

# Alarm1 : Label non defini and < 30 days
def Alarm1_CalculateFieldValues(pcmFC=CONST_WORKING_FEATURE_CLASS):
    try:
        print(" Setting {} field values to {} where ({} is {} or {}) and TODAY < DATE_DEBUT <= Today + 30 jours in {}".format(CONST_FIELDNAME_ALARMES,\
            str(CONST_CHANTIERS_LABELPCM_NONDEFINI), CONST_FIELDNAME_LABEL_PCM, CONST_FILTER1_LABEL_PCM, CONST_FILTER2_LABEL_PCM, pcmFC))

        rows = arcpy.UpdateCursor(pcmFC)
        count = 0
        for row in rows:
            OBJECTID = row.getValue(CONST_FIELDNAME_OBJECTID)
            ALARME_VALUE = row.getValue(CONST_FIELDNAME_ALARMES)
            dateDebut = row.getValue(CONST_FIELDNAME_DATEDEBUT)
            nbDays = (dateDebut - datetime.datetime.now()).days
            LabelPcm = row.getValue(CONST_FIELDNAME_LABEL_PCM)

            if nbDays > 0 and nbDays <= 30 and (LabelPcm == CONST_FILTER1_LABEL_PCM or LabelPcm == CONST_FILTER2_LABEL_PCM):
                if ALARME_VALUE == CONST_CHANTIERS_FIN_MOINS30JOURS or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS:
                    if CONST_VERBOSE == True:
                        print(" -- OID: {0}, both LABEL PCM and Fin moins de 30 jours, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)))
                    row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)
                elif ALARME_VALUE == CONST_CHANTIERS_IMPACTANTSPROCHES or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES:
                    if CONST_VERBOSE == True:
                        print(" -- OID: {0}, both Fin moins de 30 jours and Proximity, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)))
                    row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)
                elif ALARME_VALUE == CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES:
                                if CONST_VERBOSE == True:
                                    print(" -- OID: {0}, both Label non défini, Fin moins de 30 jours and Proximity, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)))
                                row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
                else:
                    row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI)

                rows.updateRow(row)
                count = count + 1

        if CONST_VERBOSE == True:
            print (" Updated rows: {0}".format(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 1 only: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 1 and 2: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 1 and 3: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 1, 2, 3: "+ str(count))

        # Delete cursor and row objects to remove locks on the data.
        del row
        del rows

        return CONST_PROCESSING_SUCCEEDED

    except:
        e = sys.exc_info()[1]
        print("Error in Alarm1_CalculateFieldValues: "+ e.args[0])
        return CONST_ERROR_CALCULATING_ALARM1

# Alarm2 : Chantiers > 3 mois dont la date de fin est dans les 4 prochaines semaines
def Alarm2_CalculateFieldValues(pcmFC=CONST_WORKING_FEATURE_CLASS):
    try:
        print(" Setting {} field values to {} where ((DATE_FIN - DATE_DEBUT) > 90 days) AND (DATE_FIN < TODAY + 30 days) in {}".format(CONST_FIELDNAME_ALARMES, str(CONST_CHANTIERS_FIN_MOINS30JOURS), pcmFC))

        rows = arcpy.UpdateCursor(pcmFC)
        count = 0
        for row in rows:
            OBJECTID = row.getValue(CONST_FIELDNAME_OBJECTID)
            ALARME_VALUE = row.getValue(CONST_FIELDNAME_ALARMES)
            dateDebut = row.getValue(CONST_FIELDNAME_DATEDEBUT)
            dateFin = row.getValue(CONST_FIELDNAME_DATEFIN)
            nbDays = (dateFin - datetime.datetime.now()).days
            workDuration = (dateFin - dateDebut).days

            if nbDays > 0 and nbDays <= 30 and workDuration > 90:
                if ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS:
                    if CONST_VERBOSE == True:
                        print(" -- OID: {0}, both LABEL PCM and Fin moins de 30 jours, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)))
                    row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)
                elif ALARME_VALUE == CONST_CHANTIERS_IMPACTANTSPROCHES or ALARME_VALUE == CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES:
                    if CONST_VERBOSE == True:
                        print(" -- OID: {0}, both Fin moins de 30 jours and Proximity, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)))
                    row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
                elif ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES:
                    if CONST_VERBOSE == True:
                        print(" -- OID: {0}, both Label non défini, Fin moins de 30 jours and Proximity, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)))
                    row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
                else:
                    row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_FIN_MOINS30JOURS)

                rows.updateRow(row)
                count = count + 1

        if CONST_VERBOSE == True:
            print (" Updated rows: {0}".format(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_FIN_MOINS30JOURS)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 2 only: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 2 and 1: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 2 and 3: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 2, 1, 3: "+ str(count))

        # Delete cursor and row objects to remove locks on the data.
        del row
        del rows

        return CONST_PROCESSING_SUCCEEDED

    except:
        e = sys.exc_info()[1]
        print("Error in Alarm2_CalculateFieldValues: "+ e.args[0])
        return CONST_ERROR_CALCULATING_ALARM2

# Alarme3: Update Field values
def Alarm3_UpdatePCMFC(gdbPathName=CONST_WORKING_PCM_CHANTIER_GDB, pcmFC=CONST_WORKING_FEATURE_CLASS, minFenetreTemps=CONST_MIN_FENETRE_TEMPS):

    try:
        # To allow overwriting output change overwriteOutput option to True.
        arcpy.env.overwriteOutput = True
        arcpy.env.workspace = gdbPathName

        fc = pcmFC

        # Process: Select Layer By Attribute (Select Layer By Attribute)
        print(" Searching rows to update")

        #fields="OBJECTID;DATE_DEBUT; DATE_FIN; ALARMES; AL_CHANTIERSPROXRESEAU"

        expression = '!'+CONST_FIELDNAME_ALCHANTIERSPROXRESEAU+'! >= 0'
        #with arcpy.SearchCursor(fc, where_clause=expression, fields="OBJECTID;DATE_DEBUT; DATE_FIN; ALARMES; AL_CHANTIERSPROXRESEAU") as cursor:
        cursor = arcpy.SearchCursor(fc)
        count = 0
        countin = 0
        lstID = []
        for row in cursor:
           if row.getValue(CONST_FIELDNAME_ALCHANTIERSPROXRESEAU) != None:
               if row.getValue(CONST_FIELDNAME_ALCHANTIERSPROXRESEAU) > 0:

                    OBJECTID = row.getValue(CONST_FIELDNAME_OBJECTID)
                    dateDebut = row.getValue(CONST_FIELDNAME_DATEDEBUT)
                    dateFin = row.getValue(CONST_FIELDNAME_DATEFIN)
                    alarme = row.getValue(CONST_FIELDNAME_ALARMES)
                    alChantierProximite = row.getValue(CONST_FIELDNAME_ALCHANTIERSPROXRESEAU)
                    dureeFenetreTemp = (dateFin-dateDebut).days

                    if dureeFenetreTemp <= minFenetreTemps :
                        if CONST_VERBOSE == True:
                            print("OID: {0}, DATE_DEBUT: {1}, DATE_FIN: {2}, FENETRE DE TEMPS TROP COURTE: {3}".format(
                        OBJECTID, dateDebut, dateFin, dureeFenetreTemp))
                        continue

                    if CONST_VERBOSE == True:
                        print("OID: {0}, DATE_DEBUT: {1}, DATE_FIN: {2}, ALARMES: {3}, AL_CHANTIERSPROXRESEAU: {4}".format(
                    OBJECTID, dateDebut, dateFin, alarme, alChantierProximite))

                    cursorin = arcpy.SearchCursor(fc)
                    rowin = cursorin.next()
                    while rowin:
                        if rowin.getValue(CONST_FIELDNAME_ALCHANTIERSPROXRESEAU) == None:
                            rowin = cursorin.next()
                            continue

                        if rowin.getValue(CONST_FIELDNAME_ALCHANTIERSPROXRESEAU) > 0:

                            OBJECTIDin = rowin.getValue(CONST_FIELDNAME_OBJECTID)
                            dateDebutin = rowin.getValue(CONST_FIELDNAME_DATEDEBUT)
                            dateFinin = rowin.getValue(CONST_FIELDNAME_DATEFIN)
                            dureeFenetreTempin = (dateFinin-dateDebutin).days

                            if OBJECTIDin == OBJECTID :
                                rowin = cursorin.next()
                                continue

                            if dureeFenetreTempin <= minFenetreTemps :
                                rowin = cursorin.next()
                                continue

                            if (dateDebutin >= dateDebut) and (dateFin - dateDebutin).days >= minFenetreTemps:
                                lstID.append(OBJECTID)
                                if CONST_VERBOSE == True:
                                    print(" -- match with OID: {0}, DATE_DEBUT: {1}, DATE_FIN: {2}, DATE DEBUT: {3}, JOUR(S) AVANT DATE FIN OID : {4}".format(
                                OBJECTIDin, dateDebutin, dateFinin, (dateFin - dateDebutin).days, OBJECTID))

                                countin = countin +1
                                break

                            if (dateFinin < dateFin) and (dateFinin - dateDebut).days >= minFenetreTemps:
                                lstID.append(OBJECTID)
                                if CONST_VERBOSE == True:
                                    print(" -- match with OID: {0}, DATE_DEBUT: {1}, DATE_FIN: {2}, DATE FIN: {3}, JOUR(S) AVANT DATE DEBUT OID : {4}".format(
                                OBJECTIDin, dateDebutin, dateFinin, (dateFinin - dateDebut).days, OBJECTID))

                                countin = countin +1
                                break

                        rowin = cursorin.next()

                    count= count+1

        print (" Found rows: {0}, match OID {1}".format(count,countin))

        print(" Updating rows")

        rows = arcpy.UpdateCursor(fc)
        count = 0
        for row in rows:
            if row.getValue(CONST_FIELDNAME_ALCHANTIERSPROXRESEAU) != None:
               if row.getValue(CONST_FIELDNAME_ALCHANTIERSPROXRESEAU) > 0:

                    OBJECTID = row.getValue(CONST_FIELDNAME_OBJECTID)
                    ALARME_VALUE = row.getValue(CONST_FIELDNAME_ALARMES)

                    for OID in lstID:
                        if OID == OBJECTID:
                            if CONST_VERBOSE == True:
                                print(" Update OID: {0}".format(OBJECTID))

                            if ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES:
                                if CONST_VERBOSE == True:
                                    print(" -- OID: {0}, both LABEL PCM and Proximity, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)))
                                row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)
                            elif ALARME_VALUE == CONST_CHANTIERS_FIN_MOINS30JOURS or ALARME_VALUE == CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES:
                                if CONST_VERBOSE == True:
                                    print(" -- OID: {0}, both Fin moins de 30 jours and Proximity, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)))
                                row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
                            elif ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES:
                                if CONST_VERBOSE == True:
                                    print(" -- OID: {0}, both Label non défini, Fin moins de 30 jours and Proximity, ALARMES field sets to {1}".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)))
                                row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
                            else:
                                row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_IMPACTANTSPROCHES)

                            rows.updateRow(row)
                            count = count + 1
                            break

        if CONST_VERBOSE == True:
            print (" Updated rows: {0}".format(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 3 only: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 3 and 1: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 3 and 2: "+ str(count))

        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarms 3, 2, 1: "+ str(count))

        # Delete cursor and row objects to remove locks on the data.
        del row
        del rows

        return CONST_PROCESSING_SUCCEEDED

    except:
        e = sys.exc_info()[1]
        print("Error in Alarme3_UpdatePCMFC: "+ e.args[0])
        return CONST_ALARME3_UPDATE_FIELD

# Alarm3: Set ALCHANTIERSPROXRESEAU field values to 1 where
# - "DATE_FIN > timestamp 'Select_Date' And (CHANTIER_PCM = 'Oui' Or CHANTIER_PCM = 'Oui_infomobilite')"
# - road works are within the Search_Distance to the road network
# - road works are within the Search_Radius each othe
def Alarm3_CalculateProximity(gdbPathName=CONST_WORKING_PCM_CHANTIER_GDB, pcmFC=CONST_WORKING_FEATURE_CLASS,
    Search_Radius=CONST_SEARCH_RADIUS, Search_Distance = CONST_SEARCH_DISTANCE, Select_Date=CONST_SELECT_DATE):
    try:

        if (Select_Date.upper() == CONST_SELECT_DATE):
            today = date.today()
            # dd/mm/YY
            Select_Date = today.strftime("%Y-%m-%d")

        Expression = CONST_PROXIMITY_SELECT_EXPRESSION_DATE + "'" + Select_Date + "'" + CONST_PROXIMITY_SELECT_EXPRESSION_CHANTIER
        print(" Selecting road works where = {}".format(Expression))

        # To allow overwriting outputs change overwriteOutput option to True.
        arcpy.env.overwriteOutput = True
        arcpy.env.workspace = gdbPathName
        RESEAU_ROUTIER = CONST_STREET_FEATURE_CLASS

        # Process: Select Layer By Attribute (Select Layer By Attribute) (management)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Selected features in "+ pcmFC + ": " + str(count))

        # Process: Select Layer By Attribute Réseau routier primaire et secondaire
        SELECTED_RESEAU_ROUTIER, count = arcpy.SelectLayerByAttribute_management(in_layer_or_view=RESEAU_ROUTIER, selection_type="NEW_SELECTION", where_clause=CONST_RESEAU_ROUTIER_EXPRESSION, invert_where_clause="")
        print(" Selected features in "+ RESEAU_ROUTIER + ": " + str(count))

        # Process: Select Layer By Location (Select Layer By Location) (management)
        print(" Selecting a subset of road works within {} meters of street network".format(str(Search_Distance)))
        Search_Distance = str(Search_Distance) + " Meters"
        PCMOB_CHANTIER_SUB_SELECT, Output_Layer_Names, count = arcpy.management.SelectLayerByLocation(in_layer=[PCMOB_CHANTIER_SELECT],
            overlap_type="INTERSECT", select_features=SELECTED_RESEAU_ROUTIER, search_distance=Search_Distance, selection_type="SUBSET_SELECTION",
            invert_spatial_relationship="NOT_INVERT")
        print(" Selected subset of features intersected: "+ str(count))

        # Process: Generate Near Table (Generate Near Table) (analysis)
        print(" Searching selected subset road works within {} meters each other".format(Search_Radius))
        Search_Radius = str(Search_Radius) + " Meters"
        PCM_GenerateNearT = CONST_NEAR_TABLE
        arcpy.analysis.GenerateNearTable(in_features=PCMOB_CHANTIER_SUB_SELECT, near_features=PCMOB_CHANTIER_SUB_SELECT, out_table=PCM_GenerateNearT,
            search_radius=Search_Radius, location="NO_LOCATION", angle="NO_ANGLE", closest="CLOSEST", closest_count=0, method="PLANAR")

        # Process: Add Join (Add Join) (management)
        print(" Joining selected subset of road works and near table based on OID")
        PCMOB_CHANTIER_JOINED = arcpy.management.AddJoin(in_layer_or_view=PCMOB_CHANTIER_SUB_SELECT, in_field=CONST_FIELDNAME_OBJECTID,
            join_table=PCM_GenerateNearT, join_field="IN_FID", join_type="KEEP_ALL")[0]
        count = arcpy.management.GetCount(PCMOB_CHANTIER_JOINED)
        print(" Joined features: "+ str(count))

        # Process: Calculate Field (Calculate Field) (management)
        codeblock = """def returnAlarmeDistance(dist):
            if dist >= 0:
                return 1
            return 0"""

        fieldToUpdate=pcmFC+"."+CONST_FIELDNAME_ALCHANTIERSPROXRESEAU
        calculateexpression="returnAlarmeDistance(!"+PCM_GenerateNearT+".NEAR_DIST!)"
        arcpy.management.CalculateField(in_table=PCMOB_CHANTIER_JOINED,
            field=fieldToUpdate,
            expression=calculateexpression,
            expression_type="PYTHON3", code_block=codeblock)
            #field_type="TEXT"
        return CONST_PROCESSING_SUCCEEDED

    except:
        e = sys.exc_info()[1]
        print("Error in Alarme3 Calculate Proximity: "+ e.args[0])
        return CONST_ALARME3_CALCULATE_PROXIMITE

# main function
if __name__ == '__main__':
    try:
        # Global Environment settings
        print("Starting Calculate Alarms Program")

        # if len(sys.argv) != 8:
        #     print("\nError missing parameters. Error code: ",str(CONST_PROGRAM_INVALID_PARAMETERS))
        #     print(" Usage: CalculateAlarms.py.py alarmCalculationOption resetAlarmField gdbPathName Pcm_Chantier_FC_Name MinFenetreTemps SelectDate SearchRadiusProximity")
        #     print(" Where:\n\
        #     - alarmCalculationOption = indicate which alarm to calculate: All= All alarms, 1= Alarm 1 only, 2= Alarm 2 only, 3= Alarm 3 only\n\
        #     - resetAlarmField = True or False. True reset the ALARMES field value to 0. False, keep current values\n\
        #     - gdbPathName = folder contaning the geodatabase\n\
        #     - Pcm_Chantier_FC_Name = geodatabase name\n\
        #     - MinFenetreTemps = minimum days\n\
        #     - SelectDate = date used to filter alarms\n\
        #     - SearchRadiusProximity = maximum distance used to search proximity road works")
        #     print("")
        #     print(" Exemple 1: set the alarm values to 0, compute all alarms (1,2,3), filters for alarm3: date= 2020-09-27, searchradius = 100 meters")
        #     print(" CalculateAlarms.py.py All True c:\temp pcm.gdb 7 2020-08-27 100")
        #     print(" Exemple 2: keep alarm values, compute alarm 3, filters for alarm3: date=today, searchradius = 100 meters")
        #     print(" CalculateAlarms.py.py 3 False c:\temp pcm.gdb 7 Today 200")
        #     sys.exit(CONST_PROGRAM_INVALID_PARAMETERS)

        #alarmCalculationOption = sys.argv[1]
        alarmCalculationOption = "All"
        #resetAlarmField = sys.argv[2]
        resetAlarmField = "True"
        # gdbPathName = sys.argv[3]
        gdbPathName = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.gdb"
        #pcmFC = sys.argv[4]
        pcmFC = "PCMOB_CHANTIER"
        #minFenetreTemps = sys.argv[5]
        minFenetreTemps = 7
        #selectDate = sys.argv[6]
        selectDate = str(date.today())
        #SearchRadius = sys.argv[7]
        SearchRadius = 100

        print("\n Parameters:")
        print(" - alarmCalculationOption: ", alarmCalculationOption)
        print(" - resetAlarmField: ", resetAlarmField)
        print(" - gdbPathName: ", gdbPathName)
        print(" - PCM_CHANTIERFC: ", pcmFC)
        print(" - MinFenetreTemps: ", minFenetreTemps)
        print(" - SelectMinDate: ", selectDate)
        print(" - SearchRadiusProximity: ", SearchRadius)

        if not os.path.exists(gdbPathName):
            print("\nError GDB not found, please verify that the GDB exists. Error code: ",str(CONST_PROGRAM_INVALID_PARAMETERS))
            sys.exit(CONST_PROGRAM_INVALID_GDBPATH)

        if alarmCalculationOption.upper() != CONST_ALARM_CALCULATION_ALL and  alarmCalculationOption.upper() != CONST_ALARM_CALCULATION_ALARM1 and\
            alarmCalculationOption.upper() != CONST_ALARM_CALCULATION_ALARM2 and  alarmCalculationOption.upper() != CONST_ALARM_CALCULATION_ALARM3:
            print("\nError invalid calculation option option. Error code: ",str(CONST_PROGRAM_INVALID_ALARM_CALCULATION_OPTION))
            sys.exit(CONST_PROGRAM_INVALID_ALARM_CALCULATION_OPTION)

        if resetAlarmField.upper() != CONST_TRUE and  resetAlarmField.upper() != CONST_FALSE:
            print("\nError invalid calculation option option. Error code: ",str(CONST_PROGRAM_INVALID_RESET_ALARM_VALUES))
            sys.exit(CONST_PROGRAM_INVALID_RESET_ALARM_VALUES)

        with arcpy.EnvManager(scratchWorkspace=gdbPathName, workspace=gdbPathName):
            if resetAlarmField.upper() == CONST_TRUE:
                errorCode = Reset_AlarmFieldValues(pcmFC)
                if errorCode > 0:
                    print("\nError during execution. Error code: ", str(errorCode))
                    sys.exit()

            if alarmCalculationOption.upper() == CONST_ALARM_CALCULATION_ALL or alarmCalculationOption == CONST_ALARM_CALCULATION_ALARM1:
                print("\nProcessing data for Alarm 1")
                errorCode = Alarm1_CalculateFieldValues(pcmFC)
                if errorCode > 0:
                    print("\nError during execution. Error code: ", str(errorCode))
                    sys.exit()

            if alarmCalculationOption.upper() == CONST_ALARM_CALCULATION_ALL or alarmCalculationOption == CONST_ALARM_CALCULATION_ALARM2:
                print("\nProcessing data for Alarm 2")
                errorCode = Alarm2_CalculateFieldValues(pcmFC)
                if errorCode > 0:
                    print("\nError during execution. Error code: ", str(errorCode))
                    sys.exit()

            if alarmCalculationOption.upper() == CONST_ALARM_CALCULATION_ALL or alarmCalculationOption == CONST_ALARM_CALCULATION_ALARM3:
                print("\nProcessing data for Alarm 3")

                errorCode = Alarm3_CalculateProximity(gdbPathName, pcmFC, Search_Radius=int(SearchRadius), Select_Date=selectDate)
                if errorCode > 0:
                    print("\nError during execution. Error code: ", str(errorCode))
                    sys.exit()

                errorCode = Alarm3_UpdatePCMFC(gdbPathName, pcmFC, int(minFenetreTemps))
                if errorCode > 0:
                    print("\nError during execution. Error code: ",str(errorCode))
                    sys.exit()

            errorCode = Compute_AlarmStatistics(pcmFC)
            if errorCode > 0:
                print("\nError during execution. Error code: ", str(errorCode))
                sys.exit()

            print("\nEnd of program successfully")

    except:
        print("Unable to run the script: error ",str(sys.exc_info()))