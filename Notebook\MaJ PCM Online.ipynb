{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Step 1 - Import new data in working database"]}, {"cell_type": "code", "execution_count": 145, "metadata": {}, "outputs": [], "source": ["import sys\n", "import arcpy\n", "from sys import argv"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Parameters to change"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [], "source": ["CONST_INPUT_PCM_CHANTIER_FC_GDB = \"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\Export PCM\\\\202111-03\\\\PCMOB_CHANTIER.gdb\\\\PCMOB_CHANTIER\"\n", "CONST_INPUT_PCM_CHANTIER_XLS = r\"C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\Export PCM\\202111-03\\11012.1 BDD PCM 20211118.xls\\T_Base_de_données$_\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Set variables"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [], "source": ["# CONSTANTS\n", "CONST_INPUT_PCM_CHANTIER_FC_JOINEDFIELD = \"NUMERO_PCM\"\n", "\n", "\n", "CONST_INPUT_PCM_CHANTIER_XLS_JOINEDFIELD = \"IDENTIFIANTS\"\n", "\n", "CONST_EXPORTED_PCM_CHANTIER_GDB = \"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.gdb\"\n", "CONST_EXPORTED_FEATURE_CLASS = \"PCMOB_CHANTIERS_EXPORTED\"\n", "\n", "CONST_WORKING_PCM_CHANTIER_GDB = \"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.gdb\"\n", "CONST_WORKING_FEATURE_CLASS = \"PCMOB_CHANTIER\"\n", "\n", "CONST_EXPORTFC_FIELDMAPPING = \"\"\n", "\n", "CONST_EXPORTFC_ALTERFIELD_1_OLD = \"F21\"\n", "CONST_EXPORTFC_ALTERFIELD_1_NEW = \"TACHES\"\n", "\n", "CONST_EXPORTFC_ALTERFIELD_2_OLD = \"COMPLEMENTS_CITEC\"\n", "CONST_EXPORTFC_ALTERFIELD_2_NEW = \"NO SOUMISSION\"\n", "\n", "CONST_EXPORTFC_ALTERFIELD_3_OLD = \"F23\"\n", "CONST_EXPORTFC_ALTERFIELD_3_NEW = \"INSPECTEUR_OCT\"\n", "\n", "CONST_EXPORTFC_ALTERFIELD_4_OLD = \"F24\"\n", "CONST_EXPORTFC_ALTERFIELD_4_NEW = \"SUSPENS_GO\"\n", "\n", "CONST_EXPORTFC_ALTERFIELD_5_OLD = \"F25\"\n", "CONST_EXPORTFC_ALTERFIELD_5_NEW = \"SUSPENS_OCT\"\n", "\n", "CONST_EXPORTFC_ALTERFIELD_6_OLD = \"F34\"\n", "CONST_EXPORTFC_ALTERFIELD_6_NEW = \"MOBILITE\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add join PCM source BDD Excel export Features"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Joining FC C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\Export PCM\\202111-03\\PCMOB_CHANTIER.gdb\\PCMOB_CHANTIER with table C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\Export PCM\\202111-03\\11012.1 BDD PCM 20211118.xls\\T_Base_de_données$_\n", " Joined features: 702\n", " Exporting joined features in FC PCMOB_CHANTIERS_EXPORTED\n", " Exported joined features 702\n", " Altering field F21 in TACHES\n", " Altering field COMPLEMENTS_CITEC in NO SOUMISSION\n", " Altering field F23 in INSPECTEUR_OCT\n", " Altering field F24 in SUSPENS_GO\n", " Altering field F25 in SUSPENS_OCT\n", " Altering field F34 in MOBILITE\n"]}], "source": ["# To allow overwriting outputs change overwriteOutput option to True.\n", "arcpy.env.overwriteOutput = True\n", "arcpy.env.addOutputsToMap = False\n", "\n", "input_fc_gdb  =CONST_INPUT_PCM_CHANTIER_FC_GDB\n", "Base_de_données = CONST_INPUT_PCM_CHANTIER_XLS\n", "PCM_Chantier_gdb = CONST_EXPORTED_PCM_CHANTIER_GDB\n", "Output_Feature_Class = CONST_EXPORTED_FEATURE_CLASS\n", "\n", "with arcpy.EnvManager(workspace=PCM_Chantier_gdb):\n", "    # Process: <PERSON>d Join (Add Join) (management)\n", "    print(\" Joining FC \"+ input_fc_gdb + \" with table \"+Base_de_données)\n", "    pcm_Chantier_Join = arcpy.management.AddJoin(in_layer_or_view=input_fc_gdb, in_field=CONST_INPUT_PCM_CHANTIER_FC_JOINEDFIELD, join_table=Base_de_données, join_field=CONST_INPUT_PCM_CHANTIER_XLS_JOINEDFIELD, join_type=\"KEEP_ALL\")[0]\n", "    count = arcpy.management.GetCount(pcm_<PERSON><PERSON>_Join)\n", "    print(\" Joined features: \"+ str(count))\n", "\n", "    # Process: Feature Class to Feature Class (Feature Class to Feature Class) (conversion)\n", "    print(\" Exporting joined features in FC \"+ CONST_EXPORTED_FEATURE_CLASS)\n", "    pcm_Chantiers_Exported = arcpy.conversion.FeatureClassToFeatureClass(in_features=pcm_<PERSON><PERSON>_Join, out_path=PCM_Chantier_gdb, out_name=Output_Feature_Class, where_clause=\"\", field_mapping= CONST_EXPORTFC_FIELDMAPPING, config_keyword=\"\")[0]\n", "    count = arcpy.management.GetCount(pcm_Chantiers_Exported)\n", "    print(\" Exported joined features \"+ str(count))\n", "\n", "    # Process: Alter Field TÂCHE (Alter Field) (management)\n", "    print(\" Altering field \" + CONST_EXPORTFC_ALTERFIELD_1_OLD + \" in \"+ CONST_EXPORTFC_ALTERFIELD_1_NEW)\n", "    arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_1_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_1_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_1_NEW, field_type=\"TEXT\", field_length=32767, field_is_nullable=\"NULL<PERSON>LE\", clear_field_alias=\"DO_NOT_CLEAR\")[0]\n", "\n", "    # Process: Alter Field NO_SOUMISSION (Alter Field) (management)\n", "    print(\" Altering field \" + CONST_EXPORTFC_ALTERFIELD_2_OLD + \" in \"+ CONST_EXPORTFC_ALTERFIELD_2_NEW)\n", "    arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_2_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_2_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_2_NEW, field_type=\"TEXT\", field_length=255, field_is_nullable=\"NULLABLE\", clear_field_alias=\"DO_NOT_CLEAR\")[0]\n", "\n", "    # Process: Alter Field INSPECTEUR_OCT (Alter Field) (management)\n", "    print(\" Altering field \" + CONST_EXPORTFC_ALTERFIELD_3_OLD + \" in \"+ CONST_EXPORTFC_ALTERFIELD_3_NEW)\n", "    arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_3_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_3_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_3_NEW, field_type=\"TEXT\", field_length=255, field_is_nullable=\"NULLABLE\", clear_field_alias=\"DO_NOT_CLEAR\")[0]\n", "\n", "    # Process: Alter Field SUSPENS_GO (Alter Field) (management)\n", "    print(\" Altering field \" + CONST_EXPORTFC_ALTERFIELD_4_OLD + \" in \"+ CONST_EXPORTFC_ALTERFIELD_4_NEW)\n", "    arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_4_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_4_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_4_NEW, field_type=\"TEXT\", field_length=255, field_is_nullable=\"NULLABLE\", clear_field_alias=\"DO_NOT_CLEAR\")[0]\n", "            \n", "    # Process: Alter Field SUSPENS_OCT (Alter Field) (management)\n", "    print(\" Altering field \" + CONST_EXPORTFC_ALTERFIELD_5_OLD + \" in \"+ CONST_EXPORTFC_ALTERFIELD_5_NEW)\n", "    arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_5_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_5_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_5_NEW, field_type=\"TEXT\", field_length=255, field_is_nullable=\"NULLABLE\", clear_field_alias=\"DO_NOT_CLEAR\")[0]\n", "            \n", "    # Process: Alter Field MOBILITE (Alter Field) (management)\n", "    print(\" Altering field \" + CONST_EXPORTFC_ALTERFIELD_6_OLD + \" in \"+ CONST_EXPORTFC_ALTERFIELD_6_NEW)\n", "    arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_6_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_6_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_6_NEW, field_type=\"TEXT\", field_length=10000, field_is_nullable=\"NULLABLE\", clear_field_alias=\"DO_NOT_CLEAR\")[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Copy Exported PCM FC to Working PCM FC"]}, {"cell_type": "code", "execution_count": 149, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Copying exported FC to working PCM Feature\n", " Deleting 702 rows in PCMOB_CHANTIER\n", " Number of rows 0 in  PCMOB_CHANTIER\n", " Adding exported features in PCMOB_CHANTIER\n", " Number of rows 702 in PCMOB_CHANTIER\n"]}], "source": ["print(\"\\nCopying exported FC to working PCM Feature\")\n", "\n", "# To allow overwriting outputs change overwriteOutput option to True.\n", "arcpy.env.overwriteOutput = True\n", "\n", "Exported_PCM_Chantier_gdb = CONST_EXPORTED_PCM_CHANTIER_GDB\n", "Exported_Feature_Class = CONST_EXPORTED_FEATURE_CLASS\n", "Working_PCM_Chantier_gdb = CONST_WORKING_PCM_CHANTIER_GDB\n", "Working_Feature_Class = CONST_WORKING_FEATURE_CLASS\n", "        \n", "with arcpy.EnvManager(workspace=Working_PCM_Chantier_gdb):\n", "    # Process: <PERSON>d Join (Add Join) (management)\n", "    working_Pcm_FC = Working_PCM_Chantier_gdb+\"\\\\\" + Working_Feature_Class\n", "    count = arcpy.management.GetCount(working_Pcm_FC)\n", "    if int(count[0]) > 0:\n", "        print(\" Deleting {} rows in {}\".format(str(count),Working_Feature_Class))\n", "        arcpy.management.DeleteRows(working_Pcm_FC)\n", "        count = arcpy.management.GetCount(working_Pcm_FC)\n", "        print(\" Number of rows {} in  {}\" .format(str(count),Working_Feature_Class))\n", "\n", "    print(\" Adding exported features in {}\".format(Working_Feature_Class))\n", "    exported_Pcm_FC = Exported_PCM_Chantier_gdb+\"\\\\\"+Exported_Feature_Class\n", "    arcpy.management.Append(exported_Pcm_FC, working_Pcm_FC, \"NO_TEST\", 'ID \"ID\" true true false 4 Long 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,ID,-1,-1;NOM \"NOM\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,NOM,0,50;TYPE_CHANTIER \"TYPE_CHANTIER\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,TYPE_CHANTIER,0,100;MOA_PCM \"MOA_PCM\" true true false 4 Long 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_PCM,-1,-1;ID_CHANTIER_MOA \"ID_CHANTIER_MOA\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,ID_CHANTIER_MOA,0,100;MOA_CONTACT \"MOA_CONTACT\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_CONTACT,0,100;MOA_TELEPHONE \"MOA_TELEPHONE\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_TELEPHONE,0,100;MOA_EMAIL \"MOA_EMAIL\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_EMAIL,0,100;COMMUNE \"COMMUNE\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COMMUNE,0,50;QUARTIER \"QUARTIER\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,QUARTIER,0,50;VOIE_NOM \"VOIE_NOM\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,VOIE_NOM,0,100;VOIE_HIERARCHIE \"VOIE_HIERARCHIE\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,VOIE_HIERARCHIE,0,50;VOIE_REMARQUE \"VOIE_REMARQUE\" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,VOIE_REMARQUE,0,1000;AFFECTE_CYCLE \"AFFECTE_CYCLE\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,AFFECTE_CYCLE,0,50;AFFECTE_TC \"AFFECTE_TC\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,AFFECTE_TC,0,50;AFFECTE_PIETON \"AFFECTE_PIETON\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,AFFECTE_PIETON,0,50;NATURE_TRAVAUX \"NATURE_TRAVAUX\" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,NATURE_TRAVAUX,0,1000;REMARQUE_TRAVAUX \"REMARQUE_TRAVAUX\" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,REMARQUE_TRAVAUX,0,1000;DATE_DEBUT \"DATE_DEBUT\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_DEBUT,-1,-1;DATE_FIN \"DATE_FIN\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_FIN,-1,-1;DATE_DUREE \"DATE_DUREE\" true true false 2 Short 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_DUREE,-1,-1;DATE_STATUT \"DATE_STATUT\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_STATUT,0,20;DATE_REMARQUE \"DATE_REMARQUE\" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_REMARQUE,0,1000;CREATION \"CREATION\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,CREATION,-1,-1;EDITION \"EDITION\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,EDITION,-1,-1;HORIZON_TEMPOREL \"HORIZON_TEMPOREL\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,HORIZON_TEMPOREL,0,50;IMPACT_GLOBAL \"IMPACT_GLOBAL\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_GLOBAL,0,20;IMPACT_TM \"IMPACT_TM\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_TM,0,20;IMPACT_TC \"IMPACT_TC\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_TC,0,20;IMPACT_REMARQUE \"IMPACT_REMARQUE\" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_REMARQUE,0,1000;IMPACT_CYCLE \"IMPACT_CYCLE\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_CYCLE,0,20;IMPACT_PIETON \"IMPACT_PIETON\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_PIETON,0,20;CHANTIER_PCM \"CHANTIER_PCM\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,CHANTIER_PCM,0,20;MOA_EXPLICITE \"MOA_EXPLICITE\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_EXPLICITE,0,50;REGION_DGM \"REGION_DGM\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,REGION_DGM,0,50;COMMUNICATION_PCM \"COMMUNICATION_PCM\" true true false 10 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COMMUNICATION_PCM,0,10;IMPACT_VALIDE \"IMPACT_VALIDE\" true true false 2 Short 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_VALIDE,-1,-1;INFO_URL \"INFO_URL\" true true false 200 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,INFO_URL,0,200;DATE_LABEL \"DATE_LABEL\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_LABEL,-1,-1;RDV_ETUDE \"RDV_ETUDE\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,RDV_ETUDE,0,50;NUMERO_PCM \"NUMERO_PCM\" true true false 4 Long 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,NUMERO_PCM,-1,-1;STATUT_CCTSS \"STATUT_CCTSS\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,STATUT_CCTSS,0,50;COM_VOIE \"COM_VOIE\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_VOIE,0,100;COM_TYPE_CHANTIER \"COM_TYPE_CHANTIER\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_TYPE_CHANTIER,0,100;COM_MOA \"COM_MOA\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_MOA,0,50;COM_DATE_DEBUT \"COM_DATE_DEBUT\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_DATE_DEBUT,-1,-1;COM_DATE_FIN \"COM_DATE_FIN\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_DATE_FIN,-1,-1;IMPACT_REMARQUE \"IMPACT_REMARQUE\" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_REMARQUE,0,1000;TACHES \"TACHES\" true true false 32000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,TACHES,0,32767;NO_SOUMISSION \"NO SOUMISSION \" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,NO_SOUMISSION,0,255;INSPECTEUR_OCT \"INSPECTEUR OCT \" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,INSPECTEUR_OCT,0,255;SUSPENS_GO \"SUSPENS_GO\" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,SUSPENS_GO,0,255;SUSPENS_OCT \"SUSPENS_OCT\" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,SUSPENS_OCT,0,255;MOBILITE \"MOBILITE\" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOBILITE,0,255;REPORTING \"REPORTING\" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,REPORTING,0,255;AL_CHANTIERSPROXRESEAU \"AL_CHANTIERSUPTROISMOIS\" true true false 8 Double 0 0,First,#;ALARMES \"ALARMES\" true true false 2 Short 0 0,First,#', '', '')\n", "    count = arcpy.management.GetCount(working_Pcm_FC)\n", "    print(\" Number of rows {} in {}\" .format(str(count),Working_Feature_Class))\n", "    \n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 2 - Calculate alarms"]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [], "source": ["import sys\n", "import arcpy\n", "import datetime\n", "from sys import argv\n", "import os\n", "from datetime import date"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Set variables"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [], "source": ["# CONSTANTS\n", "\n", "CONST_VERBOSE = False # use to display detailled information messages to console\n", "CONST_TRUE = \"TRUE\"\n", "CONST_FALSE = \"FALSE\"\n", "\n", "# ALARM CALCULATION OPTION\n", "CONST_ALARM_CALCULATION_ALL = \"ALL\"\n", "CONST_ALARM_CALCULATION_ALARM1 = \"1\"\n", "CONST_ALARM_CALCULATION_ALARM2 = \"2\"\n", "CONST_ALARM_CALCULATION_ALARM3 = \"3\"\n", "\n", "# GDB and FC settings\n", "CONST_WORKING_PCM_CHANTIER_GDB = \"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.gdb\"\n", "CONST_WORKING_FEATURE_CLASS = \"PCMOB_CHANTIER\"\n", "CONST_STREET_FEATURE_CLASS = \"RESEAU_ROUTIER\"\n", "CONST_NEAR_TABLE = \"PCM_GenerateNearT\"\n", "\n", "# Field names\n", "CONST_FIELDNAME_OBJECTID = \"OBJECTID\"\n", "CONST_FIELDNAME_DATEDEBUT = \"DATE_DEBUT\"\n", "CONST_FIELDNAME_DATEFIN = \"DATE_FIN\"\n", "CONST_FIELDNAME_ALARMES = \"ALARMES\"\n", "CONST_FIELDNAME_ALCHANTIERSPROXRESEAU = \"AL_CHANTIERSPROXRESEAU\"\n", "CONST_FIELDNAME_LABEL_PCM = \"CHANTIER_PCM\" # label PCM\n", "\n", "# select setting\n", "CONST_FILTER1_LABEL_PCM = \"Pas_defini\" # used for Alarm 1\n", "CONST_FILTER2_LABEL_PCM = \"Oui_a_confirmer\" # used for Alarm 1 Added by MAM 2020-11-18\n", "\n", "# used for Alarm 3\n", "CONST_SELECT_DATE = \"TODAY\"\n", "CONST_PROXIMITY_SELECT_EXPRESSION_DATE = \"DATE_FIN > timestamp \"\n", "CONST_RESEAU_ROUTIER_EXPRESSION = \"HIERARCHIE IN ('<PERSON><PERSON><PERSON> primaire', '<PERSON><PERSON><PERSON> secondaire')\"\n", "CONST_PROXIMITY_SELECT_EXPRESSION_CHANTIER = \" And (CHANTIER_PCM = 'Oui' Or CHANTIER_PCM = 'Oui_infomobilite')\"\n", "CONST_MIN_FENETRE_TEMPS = 7 # number of days\n", "CONST_SEARCH_DISTANCE = 5 # unit meters\n", "CONST_SEARCH_RADIUS = 100 # unit meters\n", "\n", "# Alarmes field values\n", "CONST_CHANTIERS_NA = 0\n", "CONST_CHANTIERS_LABELPCM_NONDEFINI = 1\n", "CONST_CHANTIERS_FIN_MOINS30JOURS = 2\n", "CONST_CHANTIERS_IMPACTANTSPROCHES = 3\n", "CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES = 4\n", "CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES = 5\n", "CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS = 6\n", "CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES = 7"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Reset alarms"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Resetting all alarm field values to 0 to all features in PCMOB_CHANTIER\n"]}, {"data": {"text/html": ["<div class='gpresult'><h2>Messages</h2><div id='messages' data-messages='[\"Start Time: mardi 23 novembre 2021 14:44:56\",\"Succeeded at mardi 23 novembre 2021 14:44:57 (Elapsed Time: 0,40 seconds)\"]' data-show='true'><div id = 'default' /></div></div>"], "text/plain": ["<Result 'PCMOB_CHANTIER'>"]}, "execution_count": 154, "metadata": {}, "output_type": "execute_result"}], "source": ["pcmFC=CONST_WORKING_FEATURE_CLASS\n", "\n", "print(\"\\nResetting all alarm field values to {} to all features in {}\".format(CONST_CHANTIERS_NA,pcmFC))\n", "arcpy.management.CalculateField(CONST_WORKING_FEATURE_CLASS, CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_NA, \"PYTHON3\", '')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alarm 1 - Label pas défini avant 30 jours"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Setting ALARMES field values to 1 where (CHANTIER_PCM is Pa<PERSON>_defini or Oui_a_confirmer) and TODAY < DATE_DEBUT <= Today + 30 jours in PCMOB_CHANTIER\n", " Number of alarms 1 only: 0\n", " Number of alarms 1 and 2: 0\n", " Number of alarms 1 and 3: 0\n", " Number of alarms 1, 2, 3: 0\n"]}], "source": ["pcmFC=CONST_WORKING_FEATURE_CLASS\n", "\n", "print(\" Setting {} field values to {} where ({} is {} or {}) and TODAY < DATE_DEBUT <= Today + 30 jours in {}\".format(CONST_FIELDNAME_ALARMES,\\\n", "    str(CONST_CHANTIERS_LABELPCM_NONDEFINI), CONST_FIELDNAME_LABEL_PCM, CONST_FILTER1_LABEL_PCM, CONST_FILTER2_LABEL_PCM, pcmFC))\n", "\n", "rows = arcpy.UpdateCursor(pcmFC)\n", "count = 0\n", "for row in rows:\n", "    OBJECTID = row.getValue(CONST_FIELDNAME_OBJECTID)\n", "    ALARME_VALUE = row.getValue(CONST_FIELDNAME_ALARMES)\n", "    dateDebut = row.getValue(CONST_FIELDNAME_DATEDEBUT)\n", "    nbDays = (dateDebut - datetime.datetime.now()).days\n", "    LabelPcm = row.getValue(CONST_FIELDNAME_LABEL_PCM)\n", "\n", "    if nbDays > 0 and nbDays <= 30 and (LabelPcm == CONST_FILTER1_LABEL_PCM or LabelPcm == CONST_FILTER2_LABEL_PCM):\n", "        if ALARME_VALUE == CONST_CHANTIERS_FIN_MOINS30JOURS or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS:\n", "            if CONST_VERBOSE == True:\n", "                print(\" -- OID: {0}, both LABEL PCM and Fin moins de 30 jours, ALARMES field sets to {1}\".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)))\n", "            row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)\n", "        elif ALARME_VALUE == CONST_CHANTIERS_IMPACTANTSPROCHES or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES:\n", "            if CONST_VERBOSE == True:\n", "                print(\" -- OID: {0}, both Fin moins de 30 jours and Proximity, ALARMES field sets to {1}\".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)))\n", "            row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)\n", "        elif ALARME_VALUE == CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES:\n", "                        if CONST_VERBOSE == True:\n", "                            print(\" -- OID: {0}, both Label non défini, Fin moins de 30 jours and Proximity, ALARMES field sets to {1}\".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)))\n", "                        row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)\n", "        else:\n", "            row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI)\n", "\n", "        rows.updateRow(row)\n", "        count = count + 1\n", "\n", "if CONST_VERBOSE == True:\n", "    print (\" Updated rows: {0}\".format(count))\n", "        \n", "Expression = CONST_FIELDNAME_ALARMES + \" = \" + str(CONST_CHANTIERS_LABELPCM_NONDEFINI)\n", "PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type=\"NEW_SELECTION\",\n", "    where_clause=Expression, invert_where_clause=\"\")\n", "print(\" Number of alarms 1 only: \"+ str(count))\n", "\n", "Expression = CONST_FIELDNAME_ALARMES + \" = \" + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)\n", "PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type=\"NEW_SELECTION\",\n", "    where_clause=Expression, invert_where_clause=\"\")\n", "print(\" Number of alarms 1 and 2: \"+ str(count))\n", "\n", "Expression = CONST_FIELDNAME_ALARMES + \" = \" + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES)\n", "PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type=\"NEW_SELECTION\",\n", "    where_clause=Expression, invert_where_clause=\"\")\n", "print(\" Number of alarms 1 and 3: \"+ str(count))\n", "\n", "Expression = CONST_FIELDNAME_ALARMES + \" = \" + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)\n", "PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type=\"NEW_SELECTION\",\n", "    where_clause=Expression, invert_where_clause=\"\")\n", "print(\" Number of alarms 1, 2, 3: \"+ str(count))      \n", "    \n", "# Delete cursor and row objects to remove locks on the data.\n", "del rows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alarm 2 - <PERSON><PERSON><PERSON> > 3 mois dont la date de fin est dans les 4 prochaines semaines"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Setting ALARMES field values to 2 where ((DATE_FIN - DATE_DEBUT) > 90 days) AND (DATE_FIN < TODAY + 30 days) in PCMOB_CHANTIER\n", " Number of alarms 2 only: 0\n", " Number of alarms 2 and 1: 0\n", " Number of alarms 2 and 3: 0\n", " Number of alarms 2, 1, 3: 0\n"]}], "source": ["pcmFC=CONST_WORKING_FEATURE_CLASS\n", "\n", "print(\" Setting {} field values to {} where ((DATE_FIN - DATE_DEBUT) > 90 days) AND (DATE_FIN < TODAY + 30 days) in {}\".format(CONST_FIELDNAME_ALARMES, str(CONST_CHANTIERS_FIN_MOINS30JOURS), pcmFC))\n", "\n", "rows = arcpy.UpdateCursor(pcmFC)\n", "count = 0\n", "for row in rows:\n", "    OBJECTID = row.getValue(CONST_FIELDNAME_OBJECTID)\n", "    ALARME_VALUE = row.getValue(CONST_FIELDNAME_ALARMES)\n", "    dateDebut = row.getValue(CONST_FIELDNAME_DATEDEBUT)\n", "    dateFin = row.getValue(CONST_FIELDNAME_DATEFIN)\n", "    nbDays = (dateFin - datetime.datetime.now()).days\n", "    workDuration = (dateFin - dateDebut).days\n", "            \n", "    if nbDays > 0 and nbDays <= 30 and workDuration > 90:\n", "        if ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS:\n", "            if CONST_VERBOSE == True:\n", "                print(\" -- OID: {0}, both LABEL PCM and Fin moins de 30 jours, ALARMES field sets to {1}\".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)))\n", "            row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)\n", "        elif ALARME_VALUE == CONST_CHANTIERS_IMPACTANTSPROCHES or ALARME_VALUE == CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES:\n", "            if CONST_VERBOSE == True:\n", "                print(\" -- OID: {0}, both Fin moins de 30 jours and Proximity, ALARMES field sets to {1}\".format(OBJECTID, str(CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)))\n", "            row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)\n", "        elif ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_IMPACTANTSPROCHES or ALARME_VALUE == CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES:\n", "            if CONST_VERBOSE == True:\n", "                print(\" -- OID: {0}, both Label non défini, Fin moins de 30 jours and Proximity, ALARMES field sets to {1}\".format(OBJECTID, str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)))\n", "            row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)\n", "        else:\n", "            row.setValue(CONST_FIELDNAME_ALARMES, CONST_CHANTIERS_FIN_MOINS30JOURS)\n", "\n", "        rows.updateRow(row)\n", "        count = count + 1\n", "\n", "if CONST_VERBOSE == True:\n", "    print (\" Updated rows: {0}\".format(count))\n", "        \n", "Expression = CONST_FIELDNAME_ALARMES + \" = \" + str(CONST_CHANTIERS_FIN_MOINS30JOURS)\n", "PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type=\"NEW_SELECTION\",\n", "    where_clause=Expression, invert_where_clause=\"\")\n", "print(\" Number of alarms 2 only: \"+ str(count))\n", "\n", "Expression = CONST_FIELDNAME_ALARMES + \" = \" + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS)\n", "PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type=\"NEW_SELECTION\",\n", "    where_clause=Expression, invert_where_clause=\"\")\n", "print(\" Number of alarms 2 and 1: \"+ str(count))\n", "\n", "Expression = CONST_FIELDNAME_ALARMES + \" = \" + str(CONST_CHANTIERS_FIN_MOINS30JOURS_IMPACTANTSPROCHES)\n", "PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type=\"NEW_SELECTION\",\n", "    where_clause=Expression, invert_where_clause=\"\")\n", "print(\" Number of alarms 2 and 3: \"+ str(count))\n", "\n", "Expression = CONST_FIELDNAME_ALARMES + \" = \" + str(CONST_CHANTIERS_LABELPCM_NONDEFINI_FIN_MOINS30JOURS_IMPACTANTSPROCHES)\n", "PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type=\"NEW_SELECTION\",\n", "    where_clause=Expression, invert_where_clause=\"\")\n", "print(\" Number of alarms 2, 1, 3: \"+ str(count))\n", "\n", "# Delete cursor and row objects to remove locks on the data.\n", "del rows"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Alarm 3 - calculate proximity"]}, {"cell_type": "code", "execution_count": 157, "metadata": {}, "outputs": [], "source": ["gdbPathName = CONST_WORKING_PCM_CHANTIER_GDB\n", "pcmFC = CONST_WORKING_FEATURE_CLASS\n", "Search_Radius = CONST_SEARCH_RADIUS\n", "Search_Distance = CONST_SEARCH_DISTANCE\n", "Select_Date=CONST_SELECT_DATE\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Step 3 - Update online data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Truncate layer"]}, {"cell_type": "code", "execution_count": 158, "metadata": {}, "outputs": [], "source": ["import arcpy\n", "import arcgis\n", "from arcgis.gis import GIS\n", "from arcgis.features import FeatureLayerCollection\n", "import sys\n", "import os\n", "import zipfile"]}, {"cell_type": "code", "execution_count": 159, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connecting to ArcGIS Online\n"]}], "source": ["# Parameters\n", "#AGOL account settings\n", "agolAccount = r\"https://citecing.maps.arcgis.com/\"\n", "userId = \"citecing\"\n", "userPwd = \"5I6e7!1801\"\n", "\n", "#create connection to AGOL portal GIS\n", "print(\"Connecting to ArcGIS Online\")\n", "mygis = arcgis.gis.GIS(agolAccount, userId, userPwd)\n", "\n", "#PCMOB CHANTIER FEATURE LAYER: TEST OR PRODUCTION\n", "#publishedWebLayer = r\"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/CouchesPCM_Test_Update/FeatureServer/2\" #TEST\n", "publishedWebLayer = r\"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/0\" #PRODUCTION\n", "\n", "#PCMOB CHANTIER FEATURE CLASS & GDB: TEST\n", "#updateFeatures = r\"C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\PCM_dev.gdb\\PCMOB_CHANTIER\" #TEST\n", "updateFeatures = r\"C:\\Users\\<USER>\\CITEC\\CITEC - 11012.1 PCM complément RDV étude\\4 Etude\\18 ArcGIS\\Travail\\PCM Chantier.gdb\\PCMOB_CHANTIER\"  #PRODUCTION\n"]}, {"cell_type": "code", "execution_count": 160, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Create a zip file from the GDB\n", "Zip file created\n"]}], "source": ["#Create zip file\n", "arcpy.env.overwriteOutput = True\n", "\n", "print(\"Create a zip file from the GDB\")\n", "#zip_path = \"PCM Chantier.zip\"\n", "zip_path = r\"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.zip\"\n", "#source =r\"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.gdb\"\n", "#shutil.make_archive(zip_path, \"zip\", source)\n", "zip_file = zipfile.ZipFile(zip_path, \"w\")\n", "\n", "directory = r\"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.gdb\"\n", "\n", "rootdir = os.path.basename(directory)\n", "\n", "\n", "for dirpath, dirnames, filenames in os.walk(r\"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.gdb\"):\n", "    for filename in filenames:\n", "        #new_path = dirpath[-16:]\n", "        #print(new_path)\n", "        filepath = os.path.join(dirpath, filename)\n", "        parentpath = os.path.relpath(filepath, directory)\n", "        arcname = os.path.join(rootdir, parentpath)\n", "        \n", "        zip_file.write(filepath, arcname)\n", "        #zip_file.write(os.path.join(dirpath, file))\n", "\n", "#zip_file.write(r\"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.gdb\", compress_type=zipfile.ZIP_DEFLATED)\n", "zip_file.close()\n", "print(\"Zip file created\")"]}, {"cell_type": "code", "execution_count": 161, "metadata": {}, "outputs": [{"data": {"text/html": ["<div class=\"item_container\" style=\"height: auto; overflow: hidden; border: 1px solid #cfcfcf; border-radius: 2px; background: #f6fafa; line-height: 1.21429em; padding: 10px;\">\n", "                    <div class=\"item_left\" style=\"width: 210px; float: left;\">\n", "                       <a href='https://citecing.maps.arcgis.com/home/<USER>' target='_blank'>\n", "                        <img src='http://static.arcgis.com/images/desktopapp.png' class=\"itemThumbnail\">\n", "                       </a>\n", "                    </div>\n", "\n", "                    <div class=\"item_right\"     style=\"float: none; width: auto; overflow: hidden;\">\n", "                        <a href='https://citecing.maps.arcgis.com/home/<USER>' target='_blank'><b>New PCM</b>\n", "                        </a>\n", "                        <br/><img src='https://citecing.maps.arcgis.com/home/<USER>/jsapi/esri/css/images/item_type_icons/layers16.png' style=\"vertical-align:middle;\">File Geodatabase by citecing\n", "                        <br/>Last Modified: novembre 23, 2021\n", "                        <br/>0 comments, 0 views\n", "                    </div>\n", "                </div>\n", "                "], "text/plain": ["<Item title:\"New PCM\" type:File Geodatabase owner:citecing>"]}, "execution_count": 161, "metadata": {}, "output_type": "execute_result"}], "source": ["new_pcm_properties = {\"title\": \"New PCM\", \"type\": \"File Geodatabase\", \"tags\": \"PCM\"}\n", "item_new_pcm = mygis.content.add(data=r\"C:\\\\Users\\\\<USER>\\\\CITEC\\\\CITEC - 11012.1 PCM complément RDV étude\\\\4 Etude\\\\18 ArcGIS\\\\Travail\\\\PCM Chantier.zip\", item_properties = new_pcm_properties, folder = \"PCM\")\n", "item_new_pcm"]}, {"cell_type": "code", "execution_count": 162, "metadata": {}, "outputs": [], "source": ["def Truncate<PERSON>eb<PERSON>ayer(gis=None, target=None):\n", "    try:\n", "        lyr = arcgis.features.FeatureLayer(target, gis)\n", "        lyr.manager.truncate()\n", "        print (\"Successfully truncated layer: \" + str(target))\n", "    except:\n", "        print(\"Failed truncating: \" + str(target))\n", "        sys.exit()"]}, {"cell_type": "code", "execution_count": 163, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Starting the program\n", "Truncating Web Layer\n", "Synchronization disabled\n", "Successfully truncated layer: https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/0\n", "Append data = True\n", "Synchronization enabled\n", "\n", "End of program successfully\n"]}], "source": ["print(\"\\nStarting the program\")\n", "\n", "print(\"Truncating Web Layer\")\n", "#Disable the sync of the layer\n", "url = 'https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/'\n", "pcm_flc = arcgis.features.FeatureLayerCollection(url, mygis)\n", "update_dict = {\"syncEnabled\": False}\n", "pcm_flc.manager.update_definition(update_dict)\n", "\n", "update_dict2 = {\"capabilities\": \"Create,Delete,Query,Update,Editing,Extract,ChangeTracking\"}\n", "pcm_flc.layers[0].manager.update_definition(update_dict2)\n", "print(\"Synchronization disabled\")\n", "#print(\"Capabilities : \" + str(pcm_flc.layers[0].properties.capabilities))\n", "\n", "#Truncate the layer\n", "TruncateWebLayer(mygis, publishedWebLayer)\n", "\n", "#Append the data\n", "status = pcm_flc.layers[0].append(item_id=item_new_pcm.id, upload_format=\"filegdb\", upsert = False, source_table_name=\"PCMOB_CHANTIER\")\n", "print(\"Append data = \" + str(status))\n", "\n", "#Put back the sync of the layer\n", "update_dict3 = {\"syncEnabled\": True}\n", "pcm_flc.manager.update_definition(update_dict3)\n", "\n", "ports_flc = arcgis.features.FeatureLayerCollection(url, mygis)\n", "update_dict4 = {\"capabilities\": \"Create,Delete,Query,Update,Editing,Extract,Sync,ChangeTracking\"}\n", "ports_flc.layers[0].manager.update_definition(update_dict4)\n", "print(\"Synchronization enabled\")\n", "print(\"\\nEnd of program successfully\")\n", "#print(\"Capabilities : \" + str(ports_flc.layers[0].properties.capabilities))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Append the data online"]}, {"cell_type": "code", "execution_count": 164, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Temp file deleted\n"]}], "source": ["item_new_pcm.delete()\n", "os.remove(zip_path)\n", "print(\"Temp file deleted\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ArcGISPro", "language": "Python", "name": "python3"}, "language_info": {"file_extension": ".py", "name": "python", "version": "3"}}, "nbformat": 4, "nbformat_minor": 2}