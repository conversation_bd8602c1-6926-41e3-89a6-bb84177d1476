{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Update online data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Delete all rows"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div class='gpresult'><h2>Messages</h2><div id='messages' data-messages='[\"Start Time: lundi 22 novembre 2021 16:19:31\",\"Succeeded at lundi 22 novembre 2021 16:19:33 (Elapsed Time: 2,41 seconds)\"]' data-show='true'><div id = 'default' /></div></div>"], "text/plain": ["<Result 'PCMOB CHANTIER ONLINE'>"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["arcpy.management.SelectLayerByAttribute(\"PCMOB CHANTIER ONLINE\", \"NEW_SELECTION\", \"NUMERO_PCM > 1\", None)\n", "print(\"Rows selected\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div class='gpresult'><h2>Messages</h2><div id='messages' data-messages='[\"Start Time: lundi 22 novembre 2021 16:19:33\",\"WARNING 000117: Warning empty output generated.\",\"Succeeded at lundi 22 novembre 2021 16:24:17 (Elapsed Time: 4 minutes 43 seconds)\"]' data-show='true'><div id = 'default' /></div></div>"], "text/plain": ["<Result 'PCMOB CHANTIER ONLINE'>"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["p\n", "arcpy.management.DeleteRows(\"PCMOB CHANTIER ONLINE\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Append the data online"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div class='gpresult'><h2>Messages</h2><div id='messages' data-messages='[\"Start Time: lundi 22 novembre 2021 16:24:20\",\"Succeeded at lundi 22 novembre 2021 16:24:30 (Elapsed Time: 9,50 seconds)\"]' data-show='true'><div id = 'default' /></div></div>"], "text/plain": ["<Result 'PCMOB CHANTIER ONLINE'>"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["arcpy.management.Append(\"PCMOB_CHANTIER\", \"PCMOB CHANTIER ONLINE\", \"NO_TEST\", \"TYPE_CHANTIER \\\"TYPE DU CHANTIER\\\" true true false 100 Text 0 0,First,#,PCMOB_CHANT\" +\n", "    \"IER,TYPE_CHANTIER,0,100;ID_CHANTIER_MOA \\\"N° METIER\\\" true true false 100 Text 0 0\" +\n", "    \",First,#,PCMOB_CHANTIER,ID_CHANTIER_MOA,0,100;COMMUNE \\\"COMMUNE\\\" true true false \" +\n", "    \"50 Text 0 0,First,#,PCMOB_CHANTIER,COMMUNE,0,50;VOIE_NOM \\\"VOIE\\\" true true false \" +\n", "    \"100 Text 0 0,First,#,PCMOB_CHANTIER,VOIE_NOM,0,100;DATE_DEBUT \\\"DATE DE DEBUT\\\" tr\" +\n", "    \"ue true false 8 Date 0 0,First,#,PCMOB_CHANTIER,DATE_DEBUT,-1,-1;DATE_FIN \\\"DATE \" +\n", "    \"DE FIN\\\" true true false 8 Date 0 0,First,#,PCMOB_CHANTIER,DATE_FIN,-1,-1;CHANTIE\" +\n", "    \"R_<PERSON><PERSON> \\\"LABEL PCM\\\" true true false 20 Text 0 0,First,#,PCMOB_CHANTIER,CHANTIER_PC\" +\n", "    \"M,0,20;M<PERSON>_EXPLICITE \\\"MAITRE D\\'<PERSON><PERSON><PERSON>AGE\\\" true true false 50 Text 0 0,First,#,PCMO\" +\n", "    \"B_CHANTIER,MOA_EXPLICITE,0,50;REGION_DGM \\\"REGION\\\" true true false 50 Text 0 0,Fi\" +\n", "    \"rst,#,PCMOB_CHANTIER,REGION_DGM,0,50;COMMUNICATION_PCM \\\"COMMUNICATION PCM\\\" true \" +\n", "    \"true false 10 Text 0 0,First,#,PCMOB_CHANTIER,COMMUNICATION_PCM,0,10;RDV_ETUDE \\\"\" +\n", "    \"RDV ETUDE\\\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIER,RDV_ETUDE,0,50;NUM\" +\n", "    \"ERO_PCM \\\"N° PCM\\\" true true false 0 Long 0 0,First,#,PCMOB_CHANTIER,NUMERO_PCM,-1\" +\n", "    \",-1;TACHES \\\"TACHES\\\" true true false 32000 Text 0 0,First,#,PCMOB_CHANTIER,TACHES\" +\n", "    \",0,32000;NO_SOUMISSION \\\"NO SOUMISSION \\\" true true false 255 Text 0 0,First,#,PCM\" +\n", "    \"OB_CHANTIER,NO_SOUMISSION,0,255;INSPECTEUR_OCT \\\"INSPECTEUR OCT \\\" true true false\" +\n", "    \" 255 Text 0 0,First,#,PCMOB_CHANTIER,INSPECTEUR_OCT,0,255;REPORTING \\\"REPORTING\\\" \" +\n", "    \"true true false 255 Text 0 0,First,#,PCMOB_CHANTIER,REPORTING,0,255;GlobalID \\\"Gl\" +\n", "    \"obalID\\\" false false true 38 GlobalID 0 0,First,#;AL_LBLPCM_NonDefini \\\"Chantiers \" +\n", "    \"dont le label PCM \\\" true true false 0 Long 0 0,First,#;ALARMES \\\"ALARMES\\\" true tr\" +\n", "    \"ue false 0 Short 0 0,First,#,PCMOB_CHANTIER,ALARMES,-1,-1;AL_CHANTIERSUPTROISMOI\" +\n", "    \"S \\\"ALARME CHANTIER SUP 3 MOIS\\\" true true false 0 Long 0 0,First,#;AL_CHANTIERSPR\" +\n", "    \"OXRESEAU \\\"ALARME CHANTIER PROXIMITE RESEAU\\\" true true false 0 Double 0 0,First,#\" +\n", "    \",PCMOB_CHANTIER,AL_CHANTIERSPROXRESEAU,-1,-1;SUIVI_INTERNE \\\"SUIVI INTERNE\\\" true \" +\n", "    \"true false 255 Text 0 0,First,#;VOIE_HIERARCHIE \\\"H<PERSON><PERSON><PERSON><PERSON><PERSON>\\\" true true false 50 \" +\n", "    \"Text 0 0,First,#,PCMOB_CHANTIER,VOIE_HIERARCHIE,0,50;DATE_DUREE \\\"DUREE\\\" true tru\" +\n", "    \"e false 0 Long 0 0,First,#,PCMOB_CHANTIER,DATE_DUREE,-1,-1;SUSPENS_OCT \\\"SUSPENS_\" +\n", "    \"OCT\\\" true true false 255 Text 0 0,First,#,PCMOB_CHANTIER,SUSPENS_OCT,0,255;MOBIL\" +\n", "    \"ITE \\\"MOBILITE\\\" true true false 10000 Text 0 0,First,#,PCMOB_CHANTIER,MOBILITE,0,\" +\n", "    \"10000;SUSPENS_GO \\\"SUSPENS_GO\\\" true true false 255 Text 0 0,First,#,PCMOB_CHANTIE\" +\n", "    \"R,SUSPENS_GO,0,255;COVID_19 \\\"COVID-19\\\" true true false 256 Text 0 0,First,#;NOM \" +\n", "    \"\\\"NOM DU CHANTIER\\\" true true false 256 Text 0 0,First,#,PCMOB_CHANTIER,NOM,0,50;C\" +\n", "    \"OM_VOIE \\\"COM_VOIE\\\" true true false 100 Text 0 0,First,#,PCMOB_CHANTIER,COM_VOIE,\" +\n", "    \"0,100;COM_TYPE_CHANTIER \\\"COM_TYPE_CHANTIER\\\" true true false 100 Text 0 0,First,#\" +\n", "    \",PCMOB_CHANTIER,COM_TYPE_CHANTIER,0,100;COM_DATE_DEBUT \\\"COM_DATE_DEBUT\\\" true tru\" +\n", "    \"e false 0 Date 0 0,First,#,PCMOB_CHANTIER,COM_DATE_DEBUT,-1,-1;COM_DATE_FIN \\\"COM\" +\n", "    \"_<PERSON><PERSON><PERSON>_<PERSON>IN\\\" true true false 0 Date 0 0,First,#,PCMOB_CHANTIER,COM_DATE_FIN,-1,-1;\" +\n", "    \"COM_MOA \\\"COM_MOA\\\" true true false 50 Text 0 0,First,#,PCMOB_CHANTIER,COM_MOA,0,5\" +\n", "    \"0;IMPACT_REMARQUE \\\"IMPACT_REMARQUE\\\" true true false 1000 Text 0 0,First,#,PCMOB_\" +\n", "    \"CHANTIER,IMPACT_REMARQUE,0,1000\", '', '')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ArcGISPro", "language": "Python", "name": "python3"}, "language_info": {"file_extension": ".py", "name": "python", "version": "3"}}, "nbformat": 4, "nbformat_minor": 2}