#MAM# -*- coding: utf-8 -*-
"""
Company: Citec
Author: SCH
Creation date: 2020-09-14
Update date:
Version: 1.1
Comments:
- V 1.1 : Modified AlterField_4_new from "SUIVI_INTERNE" to "SUSPENS_GO". Added AlterField_5_old = "F25", AlterField_6_new = "SUSPENS_OCT", AlterField_6_old = "F34", AlterField_5_new = "MOBILITE" 2021-03-02
"""
import sys
import arcpy
from sys import argv

# ERROR CODES
CONST_PROCESSING_SUCCEEDED = 0
CONST_ADDJOINEXPORTFC_FAILED = 1
CONST_COPY_EXPORTEDPMCFC_TO_PCMFC_FAILED = 2

# CONSTANTS
CONST_INPUT_PCM_CHANTIER_FC_GDB = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Export PCM\202507-04\PCMOB_CHANTIER.gdb\PCMOB_CHANTIER"
#CONST_INPUT_PCM_CHANTIER_FC_GDB = "C:\\temp\\PCM\\202009\\PCMOB_CHANTIER.gdb\\PCMOB_CHANTIER"
CONST_INPUT_PCM_CHANTIER_FC_JOINEDFIELD = "NUMERO_PCM"

CONST_INPUT_PCM_CHANTIER_XLS = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Export PCM\202507-04\11012.1 BDD PCM 20250724.xls\T_Base_de_données$_"
#CONST_INPUT_PCM_CHANTIER_XLS = r"C:\temp\PCM\202009\11012.1 BDD PCM 20200917.xls\T_Base_de_données$_"
CONST_INPUT_PCM_CHANTIER_XLS_JOINEDFIELD = "IDENTIFIANTS"

CONST_EXPORTED_PCM_CHANTIER_GDB = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.gdb"
#CONST_EXPORTED_PCM_CHANTIER_GDB = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM_Dev.gdb"
CONST_EXPORTED_FEATURE_CLASS = "PCMOB_CHANTIERS_EXPORTED"

CONST_WORKING_PCM_CHANTIER_GDB = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.gdb"
#CONST_WORKING_PCM_CHANTIER_GDB = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM_Dev.gdb"
CONST_WORKING_FEATURE_CLASS = "PCMOB_CHANTIER"

CONST_EXPORTFC_FIELDMAPPING = ""

CONST_EXPORTFC_ALTERFIELD_1_OLD = "F21"
CONST_EXPORTFC_ALTERFIELD_1_NEW = "TACHES"

CONST_EXPORTFC_ALTERFIELD_2_OLD = "COMPLEMENTS_CITEC"
CONST_EXPORTFC_ALTERFIELD_2_NEW = "NO SOUMISSION"

CONST_EXPORTFC_ALTERFIELD_3_OLD = "F23"
CONST_EXPORTFC_ALTERFIELD_3_NEW = "INSPECTEUR_OCT"

CONST_EXPORTFC_ALTERFIELD_4_OLD = "F24"
CONST_EXPORTFC_ALTERFIELD_4_NEW = "SUSPENS_GO"

CONST_EXPORTFC_ALTERFIELD_5_OLD = "F25"
CONST_EXPORTFC_ALTERFIELD_5_NEW = "SUSPENS_OCT"

CONST_EXPORTFC_ALTERFIELD_6_OLD = "F26"
CONST_EXPORTFC_ALTERFIELD_6_NEW = "RQ_CITEC"

CONST_EXPORTFC_ALTERFIELD_7_OLD = "F37"
CONST_EXPORTFC_ALTERFIELD_7_NEW = "MOBILITE"

# Add join between PCM new data Source (FC & BDD Excel)
# Export data in exported FC
# Rename Fields
def AddJoinPCMSourceBDDExcelExportFeatures(input_fc_gdb=CONST_INPUT_PCM_CHANTIER_FC_GDB, Base_de_données=CONST_INPUT_PCM_CHANTIER_XLS, PCM_Chantier_gdb=CONST_EXPORTED_PCM_CHANTIER_GDB, Output_Feature_Class=CONST_EXPORTED_FEATURE_CLASS):

    try:
        print("\nJoining FC-Xls data, and exporting in FC")

        # To allow overwriting outputs change overwriteOutput option to True.
        arcpy.env.overwriteOutput = True

        with arcpy.EnvManager(workspace=PCM_Chantier_gdb):
            # Process: Add Join (Add Join) (management)
            print(" Joining FC "+ input_fc_gdb + " with table "+Base_de_données)
            pcm_Chantier_Join = arcpy.management.AddJoin(in_layer_or_view=input_fc_gdb, in_field=CONST_INPUT_PCM_CHANTIER_FC_JOINEDFIELD, join_table=Base_de_données, join_field=CONST_INPUT_PCM_CHANTIER_XLS_JOINEDFIELD, join_type="KEEP_ALL")[0]
            count = arcpy.management.GetCount(pcm_Chantier_Join)
            print(" Joined features: "+ str(count))

            # Process: Feature Class to Feature Class (Feature Class to Feature Class) (conversion)
            print(" Exporting joined features in FC "+ CONST_EXPORTED_FEATURE_CLASS)
            pcm_Chantiers_Exported = arcpy.conversion.FeatureClassToFeatureClass(in_features=pcm_Chantier_Join, out_path=PCM_Chantier_gdb, out_name=Output_Feature_Class, where_clause="", field_mapping= CONST_EXPORTFC_FIELDMAPPING, config_keyword="")[0]
            count = arcpy.management.GetCount(pcm_Chantiers_Exported)
            print(" Exported joined features "+ str(count))

            # Process: Alter Field TÂCHE (Alter Field) (management)
            print(" Altering field " + CONST_EXPORTFC_ALTERFIELD_1_OLD + " in "+ CONST_EXPORTFC_ALTERFIELD_1_NEW)
            #arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_1_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_1_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_1_NEW, field_type="TEXT", field_length=32767, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]
            arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_1_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_1_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_1_NEW, field_length=32767, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]


            # Process: Alter Field NO_SOUMISSION (Alter Field) (management)
            print(" Altering field " + CONST_EXPORTFC_ALTERFIELD_2_OLD + " in "+ CONST_EXPORTFC_ALTERFIELD_2_NEW)
           # arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_2_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_2_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_2_NEW, field_type="TEXT", field_length=255, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]
            arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_2_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_2_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_2_NEW,  field_length=255, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]


            # Process: Alter Field INSPECTEUR_OCT (Alter Field) (management)
            print(" Altering field " + CONST_EXPORTFC_ALTERFIELD_3_OLD + " in "+ CONST_EXPORTFC_ALTERFIELD_3_NEW)
            #arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_3_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_3_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_3_NEW, field_type="TEXT", field_length=255, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]
            arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_3_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_3_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_3_NEW,  field_length=255, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]



            # Process: Alter Field SUSPENS_GO (Alter Field) (management)
            print(" Altering field " + CONST_EXPORTFC_ALTERFIELD_4_OLD + " in "+ CONST_EXPORTFC_ALTERFIELD_4_NEW)
            #arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_4_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_4_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_4_NEW, field_type="TEXT", field_length=255, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]
            arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_4_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_4_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_4_NEW, field_length=255, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]

            # Process: Alter Field SUSPENS_OCT (Alter Field) (management)
            print(" Altering field " + CONST_EXPORTFC_ALTERFIELD_5_OLD + " in "+ CONST_EXPORTFC_ALTERFIELD_5_NEW)
            #arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_5_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_5_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_5_NEW, field_type="TEXT", field_length=255, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]
            arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_5_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_5_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_5_NEW,  field_length=255, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]

            # Process: Alter Field RQ_CITEC (Alter Field) (management)
            print(" Altering field " + CONST_EXPORTFC_ALTERFIELD_6_OLD + " in "+ CONST_EXPORTFC_ALTERFIELD_6_NEW)
            #arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_6_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_6_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_6_NEW, field_type="TEXT", field_length=10000, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]
            arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_6_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_6_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_6_NEW,  field_length=10000, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]

            # Process: Alter Field MOBILITE (Alter Field) (management)
            print(" Altering field " + CONST_EXPORTFC_ALTERFIELD_7_OLD + " in "+ CONST_EXPORTFC_ALTERFIELD_7_NEW)
            #arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_7_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_7_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_7_NEW, field_type="TEXT", field_length=10000, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]
            arcpy.management.AlterField(in_table=pcm_Chantiers_Exported, field=CONST_EXPORTFC_ALTERFIELD_7_OLD, new_field_name=CONST_EXPORTFC_ALTERFIELD_7_NEW, new_field_alias=CONST_EXPORTFC_ALTERFIELD_7_NEW, field_length=10000, field_is_nullable="NULLABLE", clear_field_alias="DO_NOT_CLEAR")[0]

        return CONST_PROCESSING_SUCCEEDED
    except:
        e = sys.exc_info()[1]
        print(" Error in AddJoinPCMSourceBDDExcelExportFeatures: "+ e.args[0])
        return CONST_ADDJOINEXPORTFC_FAILED

# Copy exported data in working
# Export data in exported FC
# Rename Fields
def CopyExportedPmcFcToWorkingPcmFc(Exported_PCM_Chantier_gdb=CONST_EXPORTED_PCM_CHANTIER_GDB, Exported_Feature_Class=CONST_EXPORTED_FEATURE_CLASS, Working_PCM_Chantier_gdb=CONST_WORKING_PCM_CHANTIER_GDB, Working_Feature_Class=CONST_WORKING_FEATURE_CLASS):

    try:
        print("\nCopying exported FC to working PCM Feature")

        # To allow overwriting outputs change overwriteOutput option to True.
        arcpy.env.overwriteOutput = True

        with arcpy.EnvManager(workspace=Working_PCM_Chantier_gdb):
            # Process: Add Join (Add Join) (management)
            working_Pcm_FC = Working_PCM_Chantier_gdb+"\\" + Working_Feature_Class
            count = arcpy.management.GetCount(working_Pcm_FC)
            if int(count[0]) > 0:
                print(" Deleting {} rows in {}".format(str(count),Working_Feature_Class))
                arcpy.management.DeleteRows(working_Pcm_FC)
                count = arcpy.management.GetCount(working_Pcm_FC)
                print(" Number of rows {} in  {}" .format(str(count),Working_Feature_Class))

            print(" Adding exported features in {}".format(Working_Feature_Class))
            exported_Pcm_FC = Exported_PCM_Chantier_gdb+"\\"+Exported_Feature_Class
            fields_to_clean = ["MOA_CONTACT","REMARQUE_TRAVAUX"]

            with arcpy.da.UpdateCursor(exported_Pcm_FC, fields_to_clean) as cursor:
                for row in cursor:
                    if row[0]:
                        # Удаляем символы "<" и ">"
                        cleaned_value = row[0].replace("<", "").replace(">", "").replace("&", "")
                        row[0] = cleaned_value
                        cursor.updateRow(row)

            arcpy.management.Append(exported_Pcm_FC, working_Pcm_FC, "NO_TEST",'ID "ID" true true false 4 Long 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,ID,-1,-1; \
            NOM "NOM" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,NOM,0,50; \
            TYPE_CHANTIER "TYPE_CHANTIER" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,TYPE_CHANTIER,0,100;\
            MOA_PCM "MOA_PCM" true true false 4 Long 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_PCM,-1,-1;\
            ID_CHANTIER_MOA "ID_CHANTIER_MOA" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,ID_CHANTIER_MOA,0,100;\
            MOA_CONTACT "MOA_CONTACT" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_CONTACT,0,100;\
            MOA_TELEPHONE "MOA_TELEPHONE" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_TELEPHONE,0,100;\
            MOA_EMAIL "MOA_EMAIL" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_EMAIL,0,100;\
            COMMUNE "COMMUNE" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COMMUNE,0,50;\
            QUARTIER "QUARTIER" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,QUARTIER,0,50;\
            VOIE_NOM "VOIE_NOM" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,VOIE_NOM,0,100;\
            VOIE_HIERARCHIE "VOIE_HIERARCHIE" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,VOIE_HIERARCHIE,0,50;\
            VOIE_REMARQUE "VOIE_REMARQUE" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,VOIE_REMARQUE,0,1000;\
            AFFECTE_CYCLE "AFFECTE_CYCLE" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,AFFECTE_CYCLE,0,50;\
            AFFECTE_TC "AFFECTE_TC" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,AFFECTE_TC,0,50;\
            AFFECTE_PIETON "AFFECTE_PIETON" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,AFFECTE_PIETON,0,50;\
            NATURE_TRAVAUX "NATURE_TRAVAUX" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,NATURE_TRAVAUX,0,1000;\
            REMARQUE_TRAVAUX "REMARQUE_TRAVAUX" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,REMARQUE_TRAVAUX,0,1000;\
            DATE_DEBUT "DATE_DEBUT" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_DEBUT,-1,-1;\
            DATE_FIN "DATE_FIN" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_FIN,-1,-1;\
            DATE_DUREE "DATE_DUREE" true true false 2 Short 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_DUREE,-1,-1;\
            DATE_STATUT "DATE_STATUT" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_STATUT,0,20;\
            DATE_REMARQUE "DATE_REMARQUE" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_REMARQUE,0,1000;\
            CREATION "CREATION" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,CREATION,-1,-1;\
            EDITION "EDITION" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,EDITION,-1,-1;\
            HORIZON_TEMPOREL "HORIZON_TEMPOREL" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,HORIZON_TEMPOREL,0,50;\
            IMPACT_GLOBAL "IMPACT_GLOBAL" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_GLOBAL,0,20;\
            IMPACT_TM "IMPACT_TM" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_TM,0,20;\
            IMPACT_TC "IMPACT_TC" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_TC,0,20;\
            IMPACT_REMARQUE "IMPACT_REMARQUE" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_REMARQUE,0,1000;\
            IMPACT_CYCLE "IMPACT_CYCLE" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_CYCLE,0,20;\
            IMPACT_PIETON "IMPACT_PIETON" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_PIETON,0,20;\
            CHANTIER_PCM "CHANTIER_PCM" true true false 20 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,CHANTIER_PCM,0,20;\
            MOA_EXPLICITE "MOA_EXPLICITE" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOA_EXPLICITE,0,50;\
            REGION_DGM "REGION_DGM" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,REGION_DGM,0,50;\
            COMMUNICATION_PCM "COMMUNICATION_PCM" true true false 10 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COMMUNICATION_PCM,0,10;\
            IMPACT_VALIDE "IMPACT_VALIDE" true true false 2 Short 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_VALIDE,-1,-1;\
            INFO_URL "INFO_URL" true true false 200 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,INFO_URL,0,200;\
            DATE_LABEL "DATE_LABEL" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,DATE_LABEL,-1,-1;\
            RDV_ETUDE "RDV_ETUDE" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,RDV_ETUDE,0,50;\
            NUMERO_PCM "NUMERO_PCM" true true false 4 Long 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,NUMERO_PCM,-1,-1;\
            STATUT_CCTSS "STATUT_CCTSS" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,STATUT_CCTSS,0,50;\
            COM_VOIE "COM_VOIE" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_VOIE,0,100;\
            COM_TYPE_CHANTIER "COM_TYPE_CHANTIER" true true false 100 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_TYPE_CHANTIER,0,100;\
            COM_MOA "COM_MOA" true true false 50 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_MOA,0,50;\
            COM_DATE_DEBUT "COM_DATE_DEBUT" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_DATE_DEBUT,-1,-1;\
            COM_DATE_FIN "COM_DATE_FIN" true true false 8 Date 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,COM_DATE_FIN,-1,-1;\
            IMPACT_REMARQUE "IMPACT_REMARQUE" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,IMPACT_REMARQUE,0,1000;\
            TACHES "TACHES" true true false 32000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,TACHES,0,32767;\
            NO_SOUMISSION "NO SOUMISSION " true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,NO_SOUMISSION,0,255;\
            INSPECTEUR_OCT "INSPECTEUR OCT " true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,INSPECTEUR_OCT,0,255;\
            SUSPENS_GO "SUSPENS_GO" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,SUSPENS_GO,0,255;\
            SUSPENS_OCT "SUSPENS_OCT" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,SUSPENS_OCT,0,255;\
            RQ_CITEC "RQ_CITEC" true true false 1000 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,RQ_CITEC,0,1000;\
            MOBILITE "MOBILITE" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,MOBILITE,0,255;\
            REPORTING "REPORTING" true true false 255 Text 0 0,First,#,PCMOB_CHANTIERS_EXPORTED,REPORTING,0,255;\
            AL_CHANTIERSPROXRESEAU "AL_CHANTIERSUPTROISMOIS" true true false 8 Double 0 0,First,#;\
            ALARMES "ALARMES" true true false 2 Short 0 0,First,#', '', '')


            count = arcpy.management.GetCount(working_Pcm_FC)
            print(" Number of rows {} in {}" .format(str(count),Working_Feature_Class))

        return CONST_PROCESSING_SUCCEEDED
    except:
        e = sys.exc_info()[1]
        print(" Error in AddJoinPCMSourceBDDExcelExportFeatures: "+ e.args[0])
        return CONST_COPY_EXPORTEDPMCFC_TO_PCMFC_FAILED

if __name__ == '__main__':
    try:
        print("Starting PCM processing data program")

        print("#Step 1 Import New Data In Working GeoDataBase")
        errorCode = AddJoinPCMSourceBDDExcelExportFeatures()

        if errorCode==0:
            errorCode = CopyExportedPmcFcToWorkingPcmFc()

        if errorCode==0:
            print("\nEnd of program successfully")
        else:
            print("\nError during execution. Error code: ",str(errorCode))
    except:
        print("Unable to run the program: error ",str(sys.exc_info()))
