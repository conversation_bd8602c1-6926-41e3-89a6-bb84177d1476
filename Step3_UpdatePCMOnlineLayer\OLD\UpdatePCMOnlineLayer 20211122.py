import arcpy
import arcgis
from arcgis.gis import GIS
from arcgis.features import FeatureLayerCollection
import sys
import os
import json

# Parameters
#AGOL account settings
agolAccount = r"https://citecing.maps.arcgis.com/"
userId = "citecing"
userPwd = "5I6e7!1801"

#PCMOB CHANTIER FEATURE LAYER: TEST OR PRODUCTION
#publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/CouchesPCM_Test_Update/FeatureServer/2" #TEST
publishedWebLayer = r"https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/0" #PRODUCTION

#PCMOB CHANTIER FEATURE CLASS & GDB: TEST
#updateFeatures = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM_dev.gdb\PCMOB_CHANTIER" #TEST
updateFeatures = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.gdb\PCMOB_CHANTIER"  #PRODUCTION

localJSON = r"pcm_chantier.json" # temp data
localgeoJSON = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Utilitaires\Script\Step3_UpdatePCMOnlineLayer\geoJSON\pcm_chantier.geojson" # temp data

#Delete and Add data option
deleteFeatures = False #True or False: delete or no all the features in the publishedWebLayer
addFeatures = True #True or False: add/append or no new features in the publishedWebLayer from updateFeatures

def TruncateWebLayer(gis=None, target=None):
    try:
        lyr = arcgis.features.FeatureLayer(target, gis)
        lyr.manager.truncate()
        print ("Successfully truncated layer: " + str(target))
    except:
        print("Failed truncating: " + str(target))
        sys.exit()

# main function
if __name__ == '__main__':
    try:

        print("\nStarting the program")

        #create connection to AGOL portal GIS
        print("Connecting to ArcGIS Online")
        mygis = arcgis.gis.GIS(agolAccount, userId, userPwd)

        #TEST
        #Disable sync of the layer
        #url = 'https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/'
        #pcm_flc = arcgis.features.FeatureLayerCollection(url, mygis)
        #print(pcm_flc.properties)
        #update_dict = {"syncEnabled": False}
        #pcm_flc.manager.update_definition(update_dict)
        #print(pcm_flc.properties)
        #layer=pcm_flc.layers[0]

        #remove all features from the already published feature layer
        if deleteFeatures == True:
            print("Truncating Web Layer")
            #Disable the sync of the layer
            url = 'https://services7.arcgis.com/xHwxl1smQbrvQ7H9/arcgis/rest/services/Carte_PCM_WFL1/FeatureServer/'
            pcm_flc = arcgis.features.FeatureLayerCollection(url, mygis)
            update_dict = {"syncEnabled": False}
            pcm_flc.manager.update_definition(update_dict)

            update_dict2 = {"capabilities": "Create,Delete,Query,Update,Editing,Extract,ChangeTracking"}
            pcm_flc.layers[0].manager.update_definition(update_dict2)
            print("Capabilities : " + str(pcm_flc.layers[0].properties.capabilities))
            #print(ports_flc.layers[0].properties)

            #Truncate the layer
            TruncateWebLayer(mygis, publishedWebLayer)

            #Put back the sync of the layer
            update_dict3 = {"syncEnabled": True}
            pcm_flc.manager.update_definition(update_dict3)

            ports_flc = arcgis.features.FeatureLayerCollection(url, mygis)
            update_dict4 = {"capabilities": "Create,Delete,Query,Update,Editing,Extract,Sync,ChangeTracking"}
            ports_flc.layers[0].manager.update_definition(update_dict4)
            print("Capabilities : " + str(ports_flc.layers[0].properties.capabilities))

            #update_dict2 = {"syncEnabled": True}
            #ports_flc.manager.update_definition(update_dict2)
            #print("Sync capabilities : " + str(pcm_flc.properties.syncEnabled))

        if addFeatures == True:
            #reference the empty layer as FeatureLayer object from the ArcGIS Python API
            fl = arcgis.features.FeatureLayer(publishedWebLayer, mygis)

            #create a JSON object from the local features

            print("Creating a JSON file {} containing a JSON object from the local features: ".format(localJSON))
            if os.path.exists(localJSON):
                print("Deleting exsiting JSON file ".format(localJSON))
                os.remove(localJSON)

            #arcpy.FeaturesToJSON_conversion(updateFeatures, localJSON, None,None,None,"GEOJSON")
            #arcpy.conversion.JSONToFeatures(localJSON, localgeoJSON)

            #create a FeatureSet object from the JSON
            print("Creating a FeatureSet object from the JSON")
            #fs = arcgis.features.FeatureSet.from_json(json.loads(localJSON))
            #f=open(localJSON,)
            #f=json.loads(localJSON)
            #fs = arcgis.features.FeatureSet.from_json(f)
            #json_string = json.dumps(localJSON)
            #fs = arcgis.features.FeatureSet.from_geojson(localJSON)
            #print(fs)

            #add/append the local features to the hosted feature layer.
            print("Adding the local features to the hosted feature layer")
            #fl.edit_features(adds = fs)
            fl.append(localJSON, upload_format="geojson", upsert=False)

            if os.path.exists(localJSON):
                print("Deleting exsiting JSON file ".format(localJSON))
                os.remove(localJSON)

            print("\nEnd of program successfully")

    except:
        print("Unable to run the script: error ",str(sys.exc_info()))
