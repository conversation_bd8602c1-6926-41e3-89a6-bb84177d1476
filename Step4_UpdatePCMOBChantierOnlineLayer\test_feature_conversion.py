# -*- coding: utf-8 -*-
"""
Test script to verify the feature conversion fix
This script tests the read_source_features() function without connecting to ArcGIS Online
"""

import arcpy
import arcgis
import sys
import os
from datetime import datetime

# Source data from Export PCM geodatabase
sourceGdbPath = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Export PCM\202507-04\PCMOB_CHANTIER.gdb"
sourceFeatureClass = "PCMOB_CHANTIER"
sourceFeatures = os.path.join(sourceGdbPath, sourceFeatureClass)


def log_message(message):
    """Log messages with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")


def test_read_source_features():
    """Test the read_source_features function"""
    feature_layer = None
    try:
        log_message(f"Testing source data reading from: {sourceFeatures}")
        
        # Check if source data exists
        if not arcpy.Exists(sourceFeatures):
            raise Exception(f"Source feature class does not exist: {sourceFeatures}")
        
        # Get feature count for logging
        count = arcpy.management.GetCount(sourceFeatures)
        log_message(f"Found {count} features in source data")
        
        # Create a feature layer from the feature class path
        layer_name = "test_source_layer"
        log_message("Creating temporary feature layer for conversion")
        feature_layer = arcpy.management.MakeFeatureLayer(sourceFeatures, layer_name)[0]
        log_message(f"Successfully created feature layer: {feature_layer}")
        
        # Test the conversion to feature set
        log_message("Converting feature layer to feature set")
        feature_set = arcgis.features.FeatureSet.from_arcpy(feature_layer)
        
        # Validate the conversion
        if not feature_set or not hasattr(feature_set, "features"):
            raise Exception("Failed to create valid feature set from source data")
        
        feature_count = len(feature_set.features) if feature_set.features else 0
        log_message(f"Converted {feature_count} features to feature set")
        
        if feature_count == 0:
            log_message("Warning: No features found in source data")
        elif feature_count != int(str(count)):
            log_message(f"Warning: Feature count mismatch - Source: {count}, Converted: {feature_count}")
        
        # Test accessing feature properties
        if feature_set.features and len(feature_set.features) > 0:
            first_feature = feature_set.features[0]
            log_message(f"First feature attributes keys: {list(first_feature.attributes.keys())[:5]}...")
            log_message(f"First feature geometry type: {first_feature.geometry.get('type', 'Unknown')}")
        
        log_message("✅ Feature conversion test completed successfully!")
        return True
        
    except Exception as e:
        log_message(f"❌ Error in feature conversion test: {str(e)}")
        return False
    finally:
        # Clean up the temporary feature layer if it was created
        if feature_layer and arcpy.Exists(feature_layer):
            try:
                arcpy.management.Delete(feature_layer)
                log_message("Cleaned up temporary feature layer")
            except Exception as cleanup_error:
                log_message(f"Warning: Could not clean up temporary feature layer: {str(cleanup_error)}")


if __name__ == '__main__':
    try:
        log_message("Starting feature conversion test")
        
        # Set ArcPy environment
        arcpy.env.overwriteOutput = True
        
        # Run the test
        success = test_read_source_features()
        
        if success:
            log_message("🎉 All tests passed! The feature conversion fix should work.")
        else:
            log_message("⚠️ Test failed. Please check the error messages above.")
            sys.exit(1)
        
    except Exception as e:
        log_message(f"Unable to run the test: {str(e)}")
        sys.exit(1)
