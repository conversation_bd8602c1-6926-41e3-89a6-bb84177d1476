# -*- coding: utf-8 -*-
"""
Test script to verify the feature conversion fix
This script tests the read_source_features() function without connecting to ArcGIS Online
"""

import arcpy
import arcgis
import sys
import os
from datetime import datetime

# Source data from Export PCM geodatabase
sourceGdbPath = r"C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\Export PCM\202507-04\PCMOB_CHANTIER.gdb"
sourceFeatureClass = "PCMOB_CHANTIER"
sourceFeatures = os.path.join(sourceGdbPath, sourceFeatureClass)


def log_message(message):
    """Log messages with timestamp"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {message}")


def test_read_source_features():
    """Test the read_source_features function using SearchCursor approach"""
    try:
        log_message(f"Testing source data reading from: {sourceFeatures}")

        # Check if source data exists
        if not arcpy.Exists(sourceFeatures):
            raise Exception(f"Source feature class does not exist: {sourceFeatures}")

        # Get feature count for logging
        count = arcpy.management.GetCount(sourceFeatures)
        log_message(f"Found {count} features in source data")

        # Get field information
        log_message("Getting field information from source data")
        field_objects = arcpy.ListFields(sourceFeatures)

        # Create field list for cursor (exclude OID and geometry initially)
        attribute_fields = []
        for field in field_objects:
            if field.type not in [
                "OID",
                "Geometry",
            ] and not field.name.upper().startswith("SHAPE"):
                attribute_fields.append(field.name)

        log_message(f"Found {len(attribute_fields)} attribute fields")
        log_message(f"First 5 fields: {attribute_fields[:5]}")

        # Add geometry field (SHAPE@) to get geometry
        cursor_fields = attribute_fields + ["SHAPE@"]

        # Test reading a few features using SearchCursor
        log_message("Testing SearchCursor conversion (first 3 features)")
        features = []

        with arcpy.da.SearchCursor(sourceFeatures, cursor_fields) as cursor:
            for i, row in enumerate(cursor):
                if i >= 3:  # Only test first 3 features
                    break

                # Create attributes dictionary (exclude geometry)
                attributes = {}
                for j, field_name in enumerate(attribute_fields):
                    value = row[j]
                    attributes[field_name] = value

                # Get geometry (last item in row)
                geometry = row[-1]

                # Convert ArcPy geometry to dictionary format for ArcGIS API
                if geometry:
                    geom_json = geometry.JSON
                    import json

                    geom_dict = json.loads(geom_json)
                    log_message(
                        f"Feature {i+1} geometry type: {geom_dict.get('type', 'Unknown')}"
                    )
                else:
                    geom_dict = None
                    log_message(f"Feature {i+1} has no geometry")

                # Create feature dictionary
                feature_dict = {"attributes": attributes, "geometry": geom_dict}

                features.append(feature_dict)
                log_message(f"Feature {i+1} attributes count: {len(attributes)}")

        # Test creating FeatureSet
        log_message("Testing FeatureSet creation from converted features")
        feature_set = arcgis.features.FeatureSet(features)

        # Validate the feature set
        if not feature_set or not hasattr(feature_set, "features"):
            raise Exception("Failed to create valid feature set from source data")

        log_message(
            f"✅ Successfully created FeatureSet with {len(feature_set.features)} test features"
        )

        # Test accessing feature properties
        if feature_set.features and len(feature_set.features) > 0:
            first_feature = feature_set.features[0]
            log_message(
                f"First feature attributes keys: {list(first_feature.attributes.keys())[:5]}..."
            )
            if first_feature.geometry:
                log_message(
                    f"First feature geometry type: {first_feature.geometry.get('type', 'Unknown')}"
                )

        log_message("✅ Feature conversion test completed successfully!")
        return True

    except Exception as e:
        log_message(f"❌ Error in feature conversion test: {str(e)}")
        import traceback

        log_message(f"Full traceback: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    try:
        log_message("Starting feature conversion test")

        # Set ArcPy environment
        arcpy.env.overwriteOutput = True

        # Run the test
        success = test_read_source_features()

        if success:
            log_message("🎉 All tests passed! The feature conversion fix should work.")
        else:
            log_message("⚠️ Test failed. Please check the error messages above.")
            sys.exit(1)

    except Exception as e:
        log_message(f"Unable to run the test: {str(e)}")
        sys.exit(1)
