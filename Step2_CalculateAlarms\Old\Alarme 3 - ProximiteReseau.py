# -*- coding: utf-8 -*-
"""
Generated by ArcGIS ModelBuilder on : 2020-06-01 10:45:43
"""
import arcpy
from sys import argv
import os

def UpdatePCMFC(gdbPathName,pcmFC="PCMOB_CHANTIER", minFenetreTemps=7):

    try:
        # To allow overwriting output change overwriteOutput option to True.
        arcpy.env.overwriteOutput = True
        arcpy.env.workspace = gdbPathName

        fc = pcmFC

        # Process: Select Layer By Attribute (Select Layer By Attribute) 
        print(" Searching row to updape")
        
        #fields="OBJECTID;DATE_DEBUT; DATE_FIN; ALARMES; AL_CHANTIERSPROXRESEAU"

        expression = '!AL_CHANTIERSPROXRESEAU! >= 0'
        #with arcpy.SearchCursor(fc, where_clause=expression, fields="OBJECTID;DATE_DEBUT; DATE_FIN; ALARMES; AL_CHANTIERSPROXRESEAU") as cursor:
        cursor = arcpy.SearchCursor(fc)
        count = 0
        countin = 0
        lstID = []
        for row in cursor:
           if row.getValue("AL_CHANTIERSPROXRESEAU") != None:
               if row.getValue("AL_CHANTIERSPROXRESEAU") > 0:
                    
                    OBJECTID = row.getValue("OBJECTID")
                    dateDebut = row.getValue("DATE_DEBUT")
                    dateFin = row.getValue("DATE_FIN")
                    alarme = row.getValue("ALARMES")
                    alChantierProximite = row.getValue("AL_CHANTIERSPROXRESEAU")
                    dureeFenetreTemp = (dateFin-dateDebut).days

                    if dureeFenetreTemp <= minFenetreTemps :
                        print ("OID: {0}, DATE_DEBUT: {1}, DATE_FIN: {2}, FENETRE DE TEMPS TROP COURTE: {3}".format(
                        OBJECTID, dateDebut, dateFin, dureeFenetreTemp))
                        continue
                    
                    print ("OID: {0}, DATE_DEBUT: {1}, DATE_FIN: {2}, ALARMES: {3}, AL_CHANTIERSPROXRESEAU: {4}".format(
                    OBJECTID, dateDebut, dateFin, alarme, alChantierProximite))
                    
                    cursorin = arcpy.SearchCursor(fc)
                    rowin = cursorin.next()
                    while rowin:
                        if rowin.getValue("AL_CHANTIERSPROXRESEAU") == None:
                            rowin = cursorin.next()
                            continue

                        if rowin.getValue("AL_CHANTIERSPROXRESEAU") > 0:

                            OBJECTIDin = rowin.getValue("OBJECTID")
                            dateDebutin = rowin.getValue("DATE_DEBUT")
                            dateFinin = rowin.getValue("DATE_FIN")
                            dureeFenetreTempin = (dateFinin-dateDebutin).days

                            if OBJECTIDin == OBJECTID :
                                rowin = cursorin.next()
                                continue

                            if dureeFenetreTempin <= minFenetreTemps :
                                rowin = cursorin.next()
                                continue

                            if (dateDebutin >= dateDebut) and (dateFin - dateDebutin).days >= minFenetreTemps:
                                lstID.append(OBJECTID)
                                print(" -- match with OID: {0}, DATE_DEBUT: {1}, DATE_FIN: {2}, DATE DEBUT: {3}, JOUR(S) AVANT DATE FIN OID : {4}".format(
                                OBJECTIDin, dateDebutin, dateFinin, (dateFin - dateDebutin).days, OBJECTID))

                                countin = countin +1
                                break

                            if (dateFinin < dateFin) and (dateFinin - dateDebut).days >= minFenetreTemps:
                                lstID.append(OBJECTID)
                                print(" -- match with OID: {0}, DATE_DEBUT: {1}, DATE_FIN: {2}, DATE FIN: {3}, JOUR(S) AVANT DATE DEBUT OID : {4}".format(
                                OBJECTIDin, dateDebutin, dateFinin, (dateFinin - dateDebut).days, OBJECTID))

                                countin = countin +1
                                break

                        rowin = cursorin.next()
                                        
                    count= count+1
        
        print (" Found rows: {0}, match OID {1}".format(count,countin))

        print(" Updating rows")
        arcpy.management.CalculateField(pcmFC, "ALARMES", "0", "PYTHON3", '', "TEXT")

        rows = arcpy.UpdateCursor(fc)
        count = 0
        for row in rows:
            if row.getValue("AL_CHANTIERSPROXRESEAU") != None:
               if row.getValue("AL_CHANTIERSPROXRESEAU") > 0:
                
                    OBJECTID = row.getValue("OBJECTID")

                    for OID in lstID:
                        if OID == OBJECTID:
                            # field2 will be equal to field1 multiplied by 3.0
                            print(" Update ODI: {0}".format(OBJECTID))
                            row.setValue("ALARMES", 1)
                            rows.updateRow(row)
                            count = count + 1
                            break

        print (" Updated rows: {0}".format(count))
        # Delete cursor and row objects to remove locks on the data.
        del row
        del rows

        return 0

    except:
        e = sys.exc_info()[1]
        print("Error in UpdatePCMFC: "+ e.args[0])
        return 1

if __name__ == '__main__':
    try:
        # Global Environment settings
        print("Starting AlarmeProximitéRéseau script")
        
        if len(sys.argv) != 4:
            print(" Error missing parameters")
            print(" Usage: AlarmeProximitéRéseau.py gdbPathName PCM_CHANTIERFC MinFenetreTemps")
            sys.exit()

        gdbPathName = sys.argv[1]
        pcmFC = sys.argv[2]
        minFenetreTemps = sys.argv[3]
        
        print(" Parameters:")
        print(" - gdbPathName: ",gdbPathName)
        print(" - PCM_CHANTIERFC: ",pcmFC)
        print(" - MinFenetreTemps: ",minFenetreTemps)


        if not os.path.exists(gdbPathName):
            print(" Error GDB not found, please verify that the GDB exists")
            sys.exit()

        with arcpy.EnvManager(scratchWorkspace=gdbPathName, workspace=gdbPathName):
            errorCode = UpdatePCMFC(gdbPathName,pcmFC,int(minFenetreTemps))
        
            if errorCode==0:
                print(" End of program successfully")
            else:
                print("Error during execution. Error code: ",str(errorCode))

    except:
        print("Unable to run the script: error ",str(sys.exc_info()))