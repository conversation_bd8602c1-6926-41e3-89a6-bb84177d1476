
Expression
	returnNbDaysAlarm1((!DATE_DEBUT! - datetime.datetime.now()).days)

def returnNbDaysAlarm1(nbDays, lblPCM):
  rtnAlarm=-1
  
  if nbDays < 0 :
     return rtnAlarm
 
  if nbDays > 30 :
     return rtnAlarm
 
  return nbDays


        fieldToUpdate=CONST_FIELDNAME_ALARMES
        calculateexpression="returnNbDaysAlarm1((!"+CONST_FIELDNAME_DATEDEBUT+"! - datetime.datetime.now()).days)"
        codeblock = """def returnNbDaysAlarm1(nbDays):
            if nbDays > 0 :
                return 2
            elif nbDays <= 30 :
                return 2"""

        arcpy.management.CalculateField(in_table=pcmFC,
            field=fieldToUpdate,
            expression=calculateexpression,
            expression_type="PYTHON3", code_block=codeblock, field_type="TEXT")
        
        Expression = CONST_FIELDNAME_ALARMES + " = " + str(CONST_CHANTIERS_FIN_MOINS30JOURS)
        PCMOB_CHANTIER_SELECT, count = arcpy.management.SelectLayerByAttribute(in_layer_or_view=pcmFC, selection_type="NEW_SELECTION",
            where_clause=Expression, invert_where_clause="")
        print(" Number of alarm 2: "+ str(count))
