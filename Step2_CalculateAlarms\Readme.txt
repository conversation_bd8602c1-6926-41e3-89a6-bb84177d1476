Release note: 2020.09.16

Usage: CalculateAlarms.py.py alarmCalculationOption resetAlarmField gdbPathName Pcm_Chantier_FC_Name MinFenetreTemps SelectDate SearchRadiusProximity
 Where:
            - alarmCalculationOption = indicate which alarm to calculate: All= All alarms, 1= Alarm 1 only, 2= Alarm 2 only, 3= Alarm 3 only
            - resetAlarmField = True or False. True reset the ALARMES field value to 0. False, keep current values
            - gdbPathName = folder contaning the geodatabase
            - Pcm_Chantier_FC_Name = geodatabase name
            - MinFenetreTemps = minimum days
            - SelectDate = date used to filter alarms
            - SearchRadiusProximity = maximum distance used to search proximity road works

 Exemple 1: set the alarm values to 0, compute all alarms (1,2,3), filters for alarm3: date= 2020-09-27, searchradius = 100 meters
 CalculateAlarms.py All True c:\\temp pcm.gdb 7 2020-08-27 100

 Exemple 2: keep alarm values, compute alarm 3, filters for alarm3: date=today, searchradius = 200 meters
 CalculateAlarms.py 3 False c:\\temp pcm.gdb 7 Today 200

NOTE:
	In CalculateAlarms.py, set CONST_VERBOSE = True to display detailled informations

Command Doc examples:

"C:\Program Files\ArcGIS\Pro\bin\Python\envs\arcgispro-py3\python.exe" "CalculateAlarms.py" All True "C:\Users\<USER>\CITEC\CITEC - 18 ArcGIS\Travail\PCM Chantier.gdb" PCMOB_CHANTIER 7 2020-08-27 100
